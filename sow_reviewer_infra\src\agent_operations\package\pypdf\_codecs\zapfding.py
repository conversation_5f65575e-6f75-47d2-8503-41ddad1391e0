#  manually generated from https://www.unicode.org/Public/MAPPINGS/VENDORS/ADOBE/zdingbat.txt

_zapfding_encoding = [
    "\u0000",
    "\u0001",
    "\u0002",
    "\u0003",
    "\u0004",
    "\u0005",
    "\u0006",
    "\u0007",
    "\u0008",
    "\u0009",
    "\u000A",
    "\u000B",
    "\u000C",
    "\u000D",
    "\u000E",
    "\u000F",
    "\u0010",
    "\u0011",
    "\u0012",
    "\u0013",
    "\u0014",
    "\u0015",
    "\u0016",
    "\u0017",
    "\u0018",
    "\u0019",
    "\u001A",
    "\u001B",
    "\u001C",
    "\u001D",
    "\u001E",
    "\u001F",
    "\u0020",
    "\u2701",
    "\u2702",
    "\u2703",
    "\u2704",
    "\u260E",
    "\u2706",
    "\u2707",
    "\u2708",
    "\u2709",
    "\u261B",
    "\u261E",
    "\u270C",
    "\u270D",
    "\u270E",
    "\u270F",
    "\u2710",
    "\u2711",
    "\u2712",
    "\u2713",
    "\u2714",
    "\u2715",
    "\u2716",
    "\u2717",
    "\u2718",
    "\u2719",
    "\u271A",
    "\u271B",
    "\u271C",
    "\u271D",
    "\u271E",
    "\u271F",
    "\u2720",
    "\u2721",
    "\u2722",
    "\u2723",
    "\u2724",
    "\u2725",
    "\u2726",
    "\u2727",
    "\u2605",
    "\u2729",
    "\u272A",
    "\u272B",
    "\u272C",
    "\u272D",
    "\u272E",
    "\u272F",
    "\u2730",
    "\u2731",
    "\u2732",
    "\u2733",
    "\u2734",
    "\u2735",
    "\u2736",
    "\u2737",
    "\u2738",
    "\u2739",
    "\u273A",
    "\u273B",
    "\u273C",
    "\u273D",
    "\u273E",
    "\u273F",
    "\u2740",
    "\u2741",
    "\u2742",
    "\u2743",
    "\u2744",
    "\u2745",
    "\u2746",
    "\u2747",
    "\u2748",
    "\u2749",
    "\u274A",
    "\u274B",
    "\u25CF",
    "\u274D",
    "\u25A0",
    "\u274F",
    "\u2750",
    "\u2751",
    "\u2752",
    "\u25B2",
    "\u25BC",
    "\u25C6",
    "\u2756",
    "\u25D7",
    "\u2758",
    "\u2759",
    "\u275A",
    "\u275B",
    "\u275C",
    "\u275D",
    "\u275E",
    "\u007F",
    "\uF8D7",
    "\uF8D8",
    "\uF8D9",
    "\uF8DA",
    "\uF8DB",
    "\uF8DC",
    "\uF8DD",
    "\uF8DE",
    "\uF8DF",
    "\uF8E0",
    "\uF8E1",
    "\uF8E2",
    "\uF8E3",
    "\uF8E4",
    "\u008E",
    "\u008F",
    "\u0090",
    "\u0091",
    "\u0092",
    "\u0093",
    "\u0094",
    "\u0095",
    "\u0096",
    "\u0097",
    "\u0098",
    "\u0099",
    "\u009A",
    "\u009B",
    "\u009C",
    "\u009D",
    "\u009E",
    "\u009F",
    "\u00A0",
    "\u2761",
    "\u2762",
    "\u2763",
    "\u2764",
    "\u2765",
    "\u2766",
    "\u2767",
    "\u2663",
    "\u2666",
    "\u2665",
    "\u2660",
    "\u2460",
    "\u2461",
    "\u2462",
    "\u2463",
    "\u2464",
    "\u2465",
    "\u2466",
    "\u2467",
    "\u2468",
    "\u2469",
    "\u2776",
    "\u2777",
    "\u2778",
    "\u2779",
    "\u277A",
    "\u277B",
    "\u277C",
    "\u277D",
    "\u277E",
    "\u277F",
    "\u2780",
    "\u2781",
    "\u2782",
    "\u2783",
    "\u2784",
    "\u2785",
    "\u2786",
    "\u2787",
    "\u2788",
    "\u2789",
    "\u278A",
    "\u278B",
    "\u278C",
    "\u278D",
    "\u278E",
    "\u278F",
    "\u2790",
    "\u2791",
    "\u2792",
    "\u2793",
    "\u2794",
    "\u2192",
    "\u2194",
    "\u2195",
    "\u2798",
    "\u2799",
    "\u279A",
    "\u279B",
    "\u279C",
    "\u279D",
    "\u279E",
    "\u279F",
    "\u27A0",
    "\u27A1",
    "\u27A2",
    "\u27A3",
    "\u27A4",
    "\u27A5",
    "\u27A6",
    "\u27A7",
    "\u27A8",
    "\u27A9",
    "\u27AA",
    "\u27AB",
    "\u27AC",
    "\u27AD",
    "\u27AE",
    "\u27AF",
    "\u00F0",
    "\u27B1",
    "\u27B2",
    "\u27B3",
    "\u27B4",
    "\u27B5",
    "\u27B6",
    "\u27B7",
    "\u27B8",
    "\u27B9",
    "\u27BA",
    "\u27BB",
    "\u27BC",
    "\u27BD",
    "\u27BE",
    "\u00FF",
]
assert len(_zapfding_encoding) == 256

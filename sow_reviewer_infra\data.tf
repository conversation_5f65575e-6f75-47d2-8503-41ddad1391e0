data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

resource "local_file" "lambda_edge_js" {
  content  = templatefile("${path.module}/lambda_edge/raw_main.js.tmpl", {
    client_id     = aws_cognito_user_pool_client.frontend_user_pool_client.id
    cognitoDomain = "${var.resource_prefix != null ? "${var.resource_prefix}-" : ""}${var.cognito_domain_prefix}.auth.us-east-1.amazoncognito.com"
    redirectUri   = "https://${var.frontend_domain_name}/"
  })
  filename = "${path.module}/lambda_edge/main.js"
}

data "archive_file" "edge_function_code" {
  type        = "zip"
  source_file = local_file.lambda_edge_js.filename
  output_path = "${path.module}/lambda_edge/function.zip"
}
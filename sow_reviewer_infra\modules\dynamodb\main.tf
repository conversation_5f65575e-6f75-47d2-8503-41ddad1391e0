resource "aws_dynamodb_table" "document_status" {
  name           = "${var.name_prefix}${var.environment}-${var.project_name}-document-status"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "document_id"
  
  attribute {
    name = "document_id"
    type = "S"
  }

  attribute {
    name = "status"
    type = "S"
  }

  attribute {
    name = "created_at"
    type = "S"
  }

  global_secondary_index {
    name            = "StatusIndex"
    hash_key        = "status"
    range_key       = "created_at"
    projection_type = "ALL"
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}${var.environment}-${var.project_name}-document-status"
  })
}

resource "aws_dynamodb_table" "processing_results" {
  name           = "${var.name_prefix}${var.environment}-${var.project_name}-processing-results"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "document_id"
  range_key      = "agent_type"
  
  attribute {
    name = "document_id"
    type = "S"
  }

  attribute {
    name = "agent_type"
    type = "S"
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}${var.environment}-${var.project_name}-processing-results"
  })
}

# Slack scrape metadata table to track cursors and last run times per channel
resource "aws_dynamodb_table" "slack_scrape_metadata" {
  name           = "${var.name_prefix}${var.environment}-${var.project_name}-slack-metadata"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "workspace_id"
  range_key      = "channel_id"

  attribute {
    name = "workspace_id"
    type = "S"
  }

  attribute {
    name = "channel_id"
    type = "S"
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}${var.environment}-${var.project_name}-slack-metadata"
  })
}

# Slack channel cache table to store channel information and membership status
resource "aws_dynamodb_table" "slack_channel_cache" {
  name           = "${var.name_prefix}${var.environment}-${var.project_name}-slack-channel-cache"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "workspace_id"
  range_key      = "channel_id"

  attribute {
    name = "workspace_id"
    type = "S"
  }

  attribute {
    name = "channel_id"
    type = "S"
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}${var.environment}-${var.project_name}-slack-channel-cache"
  })
}

# Slack channel reviews table to store analysis results from Claude
resource "aws_dynamodb_table" "slack_channel_reviews" {
  name           = "${var.name_prefix}${var.environment}-${var.project_name}-slack-channel-reviews"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "channel_id"
  range_key      = "review_date"

  attribute {
    name = "channel_id"
    type = "S"
  }

  attribute {
    name = "review_date"
    type = "S"
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}${var.environment}-${var.project_name}-slack-channel-reviews"
  })
}
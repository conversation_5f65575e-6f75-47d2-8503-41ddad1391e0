# Test script for Slack Channel Reviewer Lambda function
# This script demonstrates how to invoke the Lambda function to review a Slack channel
#
# The Slack scraper writes files to S3 with this structure:
# slack/{workspace_id}/channels/{channel_id}_{channel_name}/date={date_prefix}/{timestamp}.txt
#
# Example: slack/T1234567890/channels/C1234567890_project-discussions/date=2024/01/15/1705312800.txt

# Configuration
$FUNCTION_NAME = "sow-reviewer-slack-channel-reviewer"  # Adjust based on your environment
$CHANNEL_ID = "C1234567890"  # Replace with actual Slack channel ID
$CHANNEL_NAME = "project-discussions"  # Replace with actual channel name

Write-Host "Testing Slack Channel Reviewer Lambda function..." -ForegroundColor Green
Write-Host "Channel ID: $CHANNEL_ID" -ForegroundColor Yellow
Write-Host "Channel Name: $CHANNEL_NAME" -ForegroundColor Yellow
Write-Host "Expected S3 path: slack/{workspace_id}/channels/${CHANNEL_ID}_${CHANNEL_NAME}/" -ForegroundColor Cyan
Write-Host ""

# Create test event
$testEvent = @{
    channel_id = $CHANNEL_ID
    channel_name = $CHANNEL_NAME
} | ConvertTo-Json

# Save test event to file
$testEvent | Out-File -FilePath "test_event.json" -Encoding UTF8

Write-Host "Test event created:" -ForegroundColor Cyan
Get-Content "test_event.json"
Write-Host ""

# Invoke Lambda function
Write-Host "Invoking Lambda function..." -ForegroundColor Green
try {
    $response = aws lambda invoke `
        --function-name $FUNCTION_NAME `
        --payload file://test_event.json `
        --cli-binary-format raw-in-base64-out `
        response.json

    Write-Host "Lambda response:" -ForegroundColor Cyan
    if (Test-Path "response.json") {
        $responseContent = Get-Content "response.json" | ConvertFrom-Json
        $responseContent | ConvertTo-Json -Depth 10
    } else {
        Write-Host "No response file generated" -ForegroundColor Red
    }
} catch {
    Write-Host "Error invoking Lambda function: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Cleaning up test files..." -ForegroundColor Yellow
if (Test-Path "test_event.json") { Remove-Item "test_event.json" }
if (Test-Path "response.json") { Remove-Item "response.json" }

Write-Host "Test completed!" -ForegroundColor Green

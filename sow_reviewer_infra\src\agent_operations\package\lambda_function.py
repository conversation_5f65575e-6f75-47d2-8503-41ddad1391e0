import json
import boto3
from boto3.dynamodb.types import TypeDeserializer
import uuid
from datetime import datetime, timezone
import os
import logging
import mimetypes
import re

# Try to use pypdf if available
try:
	from pypdf import PdfReader
	HAS_PYPDF = True
except Exception:
	HAS_PYPDF = False

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
    """
    Main handler for agent operations
    Routes to appropriate function based on the operation
    """
    try:
        logger.info(f"Agent operations event received: {json.dumps(event)}")
        logger.info(f"Context: {context}")
        
        # Extract operation from path or body
        path = event.get('path', '')
        http_method = event.get('httpMethod', '')
        
        logger.info(f"Path: {path}, Method: {http_method}")
        
        # Route based on path and method
        if '/process' in path and http_method == 'POST':
            return process_document(event, context)
        else:
            logger.error(f"Unsupported operation: {path} {http_method}")
            return {
                'statusCode': 400,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'Unsupported operation'})
            }
        
    except Exception as e:
        logger.error(f"Unexpected error in main handler: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'headers': get_cors_headers(),
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }

def process_document(event, context):
    """Process document using specified agent"""
    try:
        logger.info("Processing document with agent")
        
        # Extract parameters from event
        body = json.loads(event['body']) if isinstance(event.get('body'), str) else event.get('body', {})
        
        document_id = body.get('document_id')
        agent_type = body.get('agent_type')  # 'technical', 'financial', or 'legal'
        
        logger.info(f"Processing document_id: {document_id}, agent_type: {agent_type}")
        
        if not document_id or not agent_type:
            logger.error("Missing required parameters")
            return {
                'statusCode': 400,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'document_id and agent_type are required'})
            }
        
        if agent_type not in ['technical', 'financial', 'legal']:
            logger.error(f"Invalid agent_type: {agent_type}")
            return {
                'statusCode': 400,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'agent_type must be technical, financial, or legal'})
            }
        
        # Get document from DynamoDB
        logger.info("Retrieving document from DynamoDB")
        dynamodb = boto3.resource('dynamodb')
        doc_table = dynamodb.Table(os.environ['DOCUMENT_STATUS_TABLE'])
        
        response = doc_table.get_item(Key={'document_id': document_id})
        if 'Item' not in response:
            logger.error(f"Document not found: {document_id}")
            return {
                'statusCode': 404,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'Document not found'})
            }
        
        document = response['Item']
        s3_uri = document['s3_uri']
        file_name = document.get('file_name')
        content_type_db = document.get('content_type')
        logger.info(f"Document found, S3 URI: {s3_uri}, file_name: {file_name}, content_type: {content_type_db}")
        
        # Download document from S3
        logger.info("Downloading document from S3")
        s3_client = boto3.client('s3')
        bucket_name, s3_key = s3_uri.replace('s3://', '').split('/', 1)
        s3_response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
        file_bytes = s3_response['Body'].read()
        logger.info(f"Document downloaded, size: {len(file_bytes)} bytes")
        
        # Infer content type if missing
        if not content_type_db and file_name:
            guessed, _ = mimetypes.guess_type(file_name)
            content_type_db = guessed or 'application/octet-stream'
        
        # Extract text depending on type
        file_text = extract_text_from_bytes(file_bytes, file_name or '', content_type_db or '')
        logger.info(f"Extracted text length: {len(file_text)}")
        
        # Extract customer name using AI
        logger.info("Extracting customer name using AI")
        customer_name = extract_customer_name_ai_call(file_text)
        if not customer_name:
            customer_name = "Unknown_Customer"
            logger.warning("Could not extract customer name, using default")
        
        customer_name = customer_name.replace(" ", "_").replace("/", "_").replace("\\", "_")
        file_content_with_name = f"This SOW is for {customer_name}.\n\n{file_text}"
        logger.info(f"Customer name extracted: {customer_name}")
        
        # Invoke appropriate agent
        logger.info(f"Invoking {agent_type} agent")
        review_output = invoke_agent(agent_type, file_content_with_name)
        logger.info(f"Agent invocation completed, output length: {len(review_output) if review_output else 0}")
        
        if not review_output or review_output.startswith("Error"):
            logger.error(f"Agent invocation failed: {review_output}")
            return {
                'statusCode': 500,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': f'Agent invocation failed: {review_output}'})
            }
        
        # Save to S3 main bucket using new structure
        logger.info("Saving review to S3 main bucket")
        main_bucket = os.environ['MAIN_BUCKET_NAME']
        review_key = f"{customer_name}/output/{customer_name}_{agent_type}_review.txt"
        s3_client.put_object(
            Bucket=main_bucket,
            Key=review_key,
            Body=review_output,
            ContentType='text/plain'
        )

        # Save result to DynamoDB
        logger.info("Saving results to DynamoDB")
        results_table = dynamodb.Table(os.environ['PROCESSING_RESULTS_TABLE'])
        current_time = datetime.now(timezone.utc).isoformat()
        review_s3_uri = f"s3://{main_bucket}/{review_key}"
        results_table.put_item(
            Item={
                'document_id': document_id,
                'agent_type': agent_type,
                'customer_name': customer_name,
                'review_content': review_output,
                'created_at': current_time,
                's3_uri': review_s3_uri
            }
        )
        
        # Update document status
        logger.info("Updating document status")
        doc_table.update_item(
            Key={'document_id': document_id},
            UpdateExpression='SET updated_at = :time, #status = :status',
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':time': current_time,
                ':status': f'processed_{agent_type}'
            }
        )
        
        logger.info("Processing completed successfully")
        return {
            'statusCode': 200,
            'headers': get_cors_headers(),
            'body': json.dumps({
                'document_id': document_id,
                'agent_type': agent_type,
                'customer_name': customer_name,
                'review_s3_uri': review_s3_uri,
                'message': f'{agent_type.title()} review completed successfully'
            })
        }
        
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'headers': get_cors_headers(),
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }

def extract_text_from_bytes(file_bytes: bytes, file_name: str, content_type: str) -> str:
	lowered = (content_type or '').lower()
	if not lowered and file_name:
		guessed, _ = mimetypes.guess_type(file_name)
		lowered = (guessed or '').lower()
	
	# Text files
	if lowered.startswith('text/') or lowered == 'text/plain' or (file_name or '').lower().endswith('.txt'):
		try:
			logger.info("Using utf-8 to decode text from file")
			return file_bytes.decode('utf-8')
		except Exception:
			logger.info("Using latin-1 to decode text from file")
			return file_bytes.decode('latin-1', errors='ignore')
	
	# PDF files
	if lowered == 'application/pdf' or (file_name or '').lower().endswith('.pdf'):
		logger.info("Using pypdf to extract text from PDF")
		return extract_text_from_pdf_bytes(file_bytes)
	
	# Fallback: best-effort decode
	try:
		return file_bytes.decode('utf-8')
	except Exception:
		return file_bytes.decode('latin-1', errors='ignore')

def extract_text_from_pdf_bytes(file_bytes: bytes) -> str:
	# Prefer pypdf
	if HAS_PYPDF:
		try:
			logger.info("Using pypdf to extract text from PDF")
			import io
			reader = PdfReader(io.BytesIO(file_bytes))
			texts = []
			for page in reader.pages:
				page_text = page.extract_text() or ''
				texts.append(page_text)
			text = "\n".join(texts)
               
			# Normalize whitespace
			return re.sub(r'\s+', ' ', text).strip()
		except Exception as e:
			logger.warning(f"pypdf extraction failed, falling back: {str(e)}", exc_info=False)
	# Fallback: naive literal extraction (non-OCR)
	try:
		logger.info("Using naive literal extraction to extract text from PDF")
		data = file_bytes.decode('latin-1', errors='ignore')
		pattern = re.compile(r'\((?:\\.|[^\\\)])*\)')
		parts = pattern.findall(data)
		extracted = []
		for p in parts:
			s = p[1:-1]
			s = s.replace('\\n', '\n').replace('\\r', '\r').replace('\\t', '\t')
			s = s.replace('\\(', '(').replace('\\)', ')').replace('\\\\', '\\')
			extracted.append(s)
		text = '\n'.join(extracted)
		logger.info(f"Naive literal extraction completed, text {text}")
		return re.sub(r'\s+', ' ', text).strip()
	except Exception as e:
		logger.warning(f"Naive PDF extraction failed: {str(e)}", exc_info=False)
		return ''

def io_bytes(b: bytes):
    import io
    return io.BytesIO(b)

def extract_customer_name_ai_call(file_content):
    """Extract customer name using AI."""
    try:
        logger.info("Starting customer name extraction")
        region = os.environ.get('AWS_REGION', 'us-east-1')
        ai_client = boto3.client("bedrock-runtime", region_name=region)
        
        # Truncate content if too long
        max_length = 4000
        if len(file_content) > max_length:
            file_content = file_content[:max_length] + "..."
        
        conversation = [
            {
                "role": "user",
                "content": [{"text": f"Extract the customer name from the following Statement of Work (SOW) document. Only return the customer name, nothing else.\n\n{file_content}"}]
            }
        ]
        
        logger.info("Calling Bedrock for customer name extraction using Sonnet 4")
        response = ai_client.converse(
            modelId="us.anthropic.claude-sonnet-4-20250514-v1:0",
            messages=conversation,
            system=[
                {
                    "text": "You are an assistant that extracts customer names from SOW documents. Only return the customer name, no extra text."
                }
            ]
        )
        
        customer_name = response['output']['message']['content'][0]['text'].strip()
        logger.info(f"Customer name extracted: {customer_name}")
        return customer_name
        
    except Exception as e:
        logger.error(f"Error extracting customer name: {str(e)}", exc_info=True)
        return None

def invoke_agent(agent_type, file_content):
    """Invoke the specified agent type."""
    try:
        logger.info(f"Starting agent invocation for type: {agent_type}")
        
        # Special handling for technical agent - process channel reviews
        if agent_type == 'technical':
            return invoke_technical_agent_with_channel_reviews(file_content)
        
        # Standard agent invocation for financial and legal
        return invoke_standard_agent(agent_type, file_content)
        
    except Exception as e:
        error_msg = f"Error invoking {agent_type} agent: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return f"Error: {error_msg}"

def invoke_technical_agent_with_channel_reviews(file_content):
    """Invoke technical agent with channel reviews from DynamoDB."""
    try:
        logger.info("Starting technical agent with channel reviews processing")
        logger.info(f"Input file content length: {len(file_content) if file_content else 0}")
        
        # Get channel reviews from DynamoDB
        logger.info("Fetching channel reviews from DynamoDB...")
        channel_reviews = get_channel_reviews_from_dynamodb()
        logger.info(f"Found {len(channel_reviews)} channel reviews to process")
        
        if not channel_reviews:
            logger.warning("No channel reviews found, proceeding with standard technical agent")
            return invoke_standard_agent('technical', file_content)
        
        # Process each channel review individually
        all_results = []
        logger.info(f"Starting to process {len(channel_reviews)} channel reviews")
        for i, review in enumerate(channel_reviews):
            channel_name = review.get('channel_name', 'Unknown')
            channel_id = review.get('channel_id', 'Unknown')
            logger.info(f"Processing channel review {i+1}/{len(channel_reviews)}: {channel_name} ({channel_id})")
            
            # Combine SOW content with channel review context
            combined_content = create_combined_content(file_content, review)
            
            logger.info(f"combined_content length: {len(combined_content) if combined_content else 0}")
            
            # Log the prompt being sent to the agent
            logger.info(f"=== PROMPT FOR CHANNEL {channel_name} ===")
            logger.info(combined_content)
            logger.info(f"=== END PROMPT FOR CHANNEL {channel_name} ===")
            
            # Invoke agent for this specific channel review
            logger.info(f"Invoking technical agent for channel: {channel_name}")
            result = invoke_standard_agent('technical', combined_content)
            
            if result and not result.startswith("Error"):
                logger.info(f"Successfully processed channel review {i+1}: {channel_name}")
                logger.info(f"=== AGENT RESPONSE FOR CHANNEL {channel_name} ===")
                logger.info(result)
                logger.info(f"=== END AGENT RESPONSE FOR CHANNEL {channel_name} ===")
                all_results.append({
                    'channel_name': review.get('channel_name', 'Unknown'),
                    'channel_id': review.get('channel_id', 'Unknown'),
                    'result': result
                })
            else:
                logger.error(f"Failed to process channel review {i+1} ({channel_name}): {result}")
        
        # Combine all results into a comprehensive response
        logger.info(f"Processing complete. Successful results: {len(all_results)}/{len(channel_reviews)}")
        
        if all_results:
            logger.info("Combining channel review results into final response")
            final_result = combine_channel_review_results(all_results)
            logger.info("=== FINAL COMBINED RESULT ===")
            logger.info(final_result)
            logger.info("=== END FINAL COMBINED RESULT ===")
            return final_result
        else:
            logger.error("All channel review processing failed, falling back to standard processing")
            return invoke_standard_agent('technical', file_content)
            
    except Exception as e:
        error_msg = f"Error in technical agent with channel reviews: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return f"Error: {error_msg}"

def get_channel_reviews_from_dynamodb():
    """Retrieve channel reviews from DynamoDB (AttributeValue format aware)."""
    try:
        logger.info("Retrieving channel reviews from DynamoDB")
        table_name = os.environ['SLACK_CHANNEL_REVIEWS_TABLE']
        logger.info(f"Scanning DynamoDB table: {table_name}")

        # Use low-level client to be resilient to AttributeValue structure
        dynamodb_client = boto3.client('dynamodb')
        deserializer = TypeDeserializer()

        paginator = dynamodb_client.get_paginator('scan')
        reviews: list[dict] = []
        page_count = 0

        for page in paginator.paginate(TableName=table_name):
            page_count += 1
            items = page.get('Items', [])
            logger.info(f"Page {page_count}: raw items {len(items)}")

            for av_item in items:
                # Deserialize AttributeValue map to plain Python types
                item: dict = {k: deserializer.deserialize(v) for k, v in av_item.items()}

                # Only include successful reviews
                if item.get('status') != 'success':
                    continue

                # Ensure analysis_results is a dict
                analysis_results = item.get('analysis_results')
                if isinstance(analysis_results, str):
                    try:
                        analysis_results = json.loads(analysis_results)
                    except json.JSONDecodeError:
                        analysis_results = {}
                elif analysis_results is None:
                    analysis_results = {}

                item['analysis_results'] = analysis_results
                reviews.append(item)

            logger.info(f"Accumulated items after page {page_count}: {len(reviews)}")

        logger.info(f"Total retrieved {len(reviews)} successful channel reviews across {page_count} pages")

        # Log a brief summary of first few items
        for i, review in enumerate(reviews[:5]):
            logger.info(
                json.dumps({
                    'index': i + 1,
                    'channel_id': review.get('channel_id'),
                    'channel_name': review.get('channel_name'),
                    'review_date': review.get('review_date'),
                    'has_analysis': bool(review.get('analysis_results')),
                })
            )

        return reviews

    except Exception as e:
        logger.error(f"Error retrieving channel reviews from DynamoDB: {str(e)}", exc_info=True)
        return []

def create_combined_content(sow_content, channel_review):
    """Combine SOW content with channel review context."""
    try:
        channel_name = channel_review.get('channel_name', 'Unknown Channel')
        analysis_results = channel_review.get('analysis_results', '{}')
        
        # Parse the analysis results if it's a string
        if isinstance(analysis_results, str):
            try:
                analysis_results = json.loads(analysis_results)
            except json.JSONDecodeError:
                analysis_results = {}
        
        # Extract the structured data
        good_points = analysis_results.get('good_points', [])
        bad_points = analysis_results.get('bad_points', [])
        recommendations = analysis_results.get('recommendations', [])
        
        # Create context from channel review
        channel_context = f"""
CHANNEL REVIEW CONTEXT - {channel_name}:
This channel review provides insights from previous project discussions and communications.

GOOD POINTS FROM CHANNEL:
{chr(10).join([f"• {point}" for point in good_points]) if good_points else "• No specific good points identified"}

BAD POINTS FROM CHANNEL:
{chr(10).join([f"• {point}" for point in bad_points]) if bad_points else "• No specific bad points identified"}

RECOMMENDATIONS FROM CHANNEL:
{chr(10).join([f"• {point}" for point in recommendations]) if recommendations else "• No specific recommendations identified"}

---
SOW DOCUMENT TO REVIEW:
{sow_content}
"""
        
        return channel_context
        
    except Exception as e:
        logger.error(f"Error creating combined content: {str(e)}", exc_info=True)
        return sow_content

def combine_channel_review_results(results):
    """Combine multiple channel review results into a comprehensive response."""
    try:
        logger.info(f"Combining {len(results)} channel review results")
        
        combined_response = "TECHNICAL REVIEW COMPILED FROM MULTIPLE CHANNEL INSIGHTS\n"
        combined_response += "=" * 60 + "\n\n"
        
        for i, result in enumerate(results, 1):
            channel_name = result['channel_name']
            channel_result = result['result']
            
            combined_response += f"CHANNEL {i}: {channel_name}\n"
            combined_response += "-" * 40 + "\n"
            combined_response += channel_result
            combined_response += "\n\n"
        
        combined_response += "=" * 60 + "\n"
        combined_response += f"SUMMARY: Technical review completed using insights from {len(results)} channels.\n"
        combined_response += "Each channel provided unique perspectives on project management, communication patterns, and technical considerations."
        
        logger.info(f"Successfully combined {len(results)} channel review results")
        return combined_response
        
    except Exception as e:
        logger.error(f"Error combining channel review results: {str(e)}", exc_info=True)
        return f"Error combining results: {str(e)}"

def invoke_standard_agent(agent_type, file_content):
    """Invoke the specified agent type using standard method."""
    try:
        logger.info(f"Starting standard agent invocation for type: {agent_type}")
        region = os.environ.get('AWS_REGION', 'us-east-1')
        environment = os.environ.get('ENVIRONMENT', 'dev')
        
        logger.info(f"Environment: {environment}, Region: {region}")
        
        # Use bedrock-agent-runtime client for agent invocation
        agent_runtime_client = boto3.client("bedrock-agent-runtime", region_name=region)
        agent_client = boto3.client("bedrock-agent", region_name=region)
        
        # Map agent types to agent names (based on environment)
        agent_name_map = {
            'technical': f"{environment}-sow-reviewer-tech-agent",
            'financial': f"{environment}-sow-reviewer-financial-agent",
            'legal': f"{environment}-sow-reviewer-legal-agent"
        }
        
        agent_name = agent_name_map[agent_type]
        logger.info(f"Looking for agent: {agent_name}")
        
        # Find agent ID and alias
        logger.info("Listing agents")
        agents = agent_client.list_agents()["agentSummaries"]
        logger.info(f"Found {len(agents)} agents")
        
        agent_id = None
        for agent in agents:
            logger.info(f"Agent: {agent['agentName']} (ID: {agent['agentId']})")
            if agent["agentName"] == agent_name:
                agent_id = agent["agentId"]
                break
        
        if not agent_id:
            error_msg = f"Agent {agent_name} not found. Available agents: {[a['agentName'] for a in agents]}"
            logger.error(error_msg)
            return f"Error: {error_msg}"
        
        logger.info(f"Found agent ID: {agent_id}")
        
        # Get agent aliases
        logger.info("Getting agent aliases")
        aliases = agent_client.list_agent_aliases(agentId=agent_id)
        if not aliases['agentAliasSummaries']:
            error_msg = f"No aliases found for agent {agent_name}"
            logger.error(error_msg)
            return f"Error: {error_msg}"
        
        alias_id = aliases['agentAliasSummaries'][0]['agentAliasId']
        logger.info(f"Using alias ID: {alias_id}")
        
        # Truncate content if too long for agent
        max_length = 8000
        if len(file_content) > max_length:
            file_content = file_content[:max_length] + "..."
        
        # Invoke agent
        logger.info("Invoking agent")
        session_id = str(uuid.uuid4())
        logger.info(f"Session ID: {session_id}")
        
        response = agent_runtime_client.invoke_agent(
            agentId=agent_id,
            agentAliasId=alias_id,
            inputText=file_content,
            sessionId=session_id,
        )
        
        logger.info("Agent response received, processing completion")
        
        # Process response
        result = ""
        for event in response['completion']:
            if 'chunk' in event:
                chunk = event['chunk']
                if 'bytes' in chunk:
                    result += chunk['bytes'].decode('utf-8')
        
        logger.info(f"Agent processing completed, result length: {len(result)}")
        return result
        
    except Exception as e:
        error_msg = f"Error invoking {agent_type} agent: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return f"Error: {error_msg}"

def get_cors_headers():
    """Get standard CORS headers"""
    return {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS'
    }

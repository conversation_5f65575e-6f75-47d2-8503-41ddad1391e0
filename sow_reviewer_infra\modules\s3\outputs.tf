output "main_bucket_name" {
  description = "Name of the main S3 bucket for SOW operations"
  value       = aws_s3_bucket.sow_main_bucket.id
}

output "main_bucket_arn" {
  description = "ARN of the main S3 bucket for SOW operations"
  value       = aws_s3_bucket.sow_main_bucket.arn
}

# Backward compatibility outputs
output "documents_bucket_name" {
  description = "Name of the S3 bucket for storing documents (backward compatibility)"
  value       = aws_s3_bucket.sow_main_bucket.id
}

output "documents_bucket_arn" {
  description = "ARN of the S3 bucket for storing documents (backward compatibility)"
  value       = aws_s3_bucket.sow_main_bucket.arn
}

output "reviews_bucket_name" {
  description = "Name of the S3 bucket for storing reviews (backward compatibility)"
  value       = aws_s3_bucket.sow_main_bucket.id
}

output "reviews_bucket_arn" {
  description = "ARN of the S3 bucket for storing reviews (backward compatibility)"
  value       = aws_s3_bucket.sow_main_bucket.arn
}

output "slack_bucket_name" {
  description = "Name of the dedicated Slack data S3 bucket"
  value       = aws_s3_bucket.slack_data_bucket.id
}

output "slack_bucket_arn" {
  description = "ARN of the dedicated Slack data S3 bucket"
  value       = aws_s3_bucket.slack_data_bucket.arn
}
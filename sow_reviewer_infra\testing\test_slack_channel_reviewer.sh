#!/bin/bash

# Test script for Slack Channel Reviewer Lambda function
# This script demonstrates how to invoke the Lambda function to review a Slack channel
#
# The Slack scraper writes files to S3 with this structure:
# slack/{workspace_id}/channels/{channel_id}_{channel_name}/date={date_prefix}/{timestamp}.txt
#
# Example: slack/T1234567890/channels/C1234567890_project-discussions/date=2024/01/15/1705312800.txt

# Configuration
FUNCTION_NAME="sow-reviewer-slack-channel-reviewer"  # Adjust based on your environment
CHANNEL_ID="C1234567890"  # Replace with actual Slack channel ID
CHANNEL_NAME="project-discussions"  # Replace with actual channel name

echo "Testing Slack Channel Reviewer Lambda function..."
echo "Channel ID: $CHANNEL_ID"
echo "Channel Name: $CHANNEL_NAME"
echo "Expected S3 path: slack/{workspace_id}/channels/${CHANNEL_ID}_${CHANNEL_NAME}/"
echo ""

# Create test event
cat > test_event.json << EOF
{
  "channel_id": "$CHANNEL_ID",
  "channel_name": "$CHANNEL_NAME"
}
EOF

echo "Test event created:"
cat test_event.json
echo ""

# Invoke Lambda function
echo "Invoking Lambda function..."
aws lambda invoke \
  --function-name "$FUNCTION_NAME" \
  --payload file://test_event.json \
  --cli-binary-format raw-in-base64-out \
  response.json

echo ""
echo "Lambda response:"
cat response.json | jq '.'

echo ""
echo "Cleaning up test files..."
rm -f test_event.json response.json

echo "Test completed!"

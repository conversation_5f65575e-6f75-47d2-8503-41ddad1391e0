#!/bin/bash

# SOW Reviewer - Status Test Script
# Tests document status checking

set -e

# Configuration
API_URL="${SOW_API_URL:-https://x2m7lxjuph.execute-api.us-east-1.amazonaws.com/dev}"

echo "=== SOW Reviewer Status Test ==="
echo "API URL: $API_URL"
echo ""

# Get document ID parameter
DOCUMENT_ID="$1"

# Use last document ID if not provided
if [ -z "$DOCUMENT_ID" ] && [ -f ".last_document_id" ]; then
    DOCUMENT_ID=$(cat .last_document_id)
fi

# Validate parameter
if [ -z "$DOCUMENT_ID" ]; then
    echo "❌ Document ID required!"
    echo "Usage: $0 <document_id>"
    echo ""
    echo "Or run ./test_upload.sh first to create a test document"
    exit 1
fi

echo "Document ID: $DOCUMENT_ID"
echo ""

echo "Checking document status..."
echo ""

# Get document status
RESPONSE=$(curl -s -X GET "$API_URL/status/$DOCUMENT_ID")

echo "Response:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

# Parse response
STATUS=$(echo "$RESPONSE" | jq -r '.status' 2>/dev/null || echo "")
FILE_NAME=$(echo "$RESPONSE" | jq -r '.file_name' 2>/dev/null || echo "")
CUSTOMER_NAME=$(echo "$RESPONSE" | jq -r '.customer_name' 2>/dev/null || echo "")
PROCESSING_RESULTS=$(echo "$RESPONSE" | jq -r '.processing_results' 2>/dev/null || echo "")

if [ "$STATUS" != "null" ] && [ -n "$STATUS" ]; then
    echo ""
    echo "✅ Status check successful!"
    echo ""
    echo "Document Details:"
    echo "  Status: $STATUS"
    echo "  File Name: $FILE_NAME"
    echo "  Customer: $CUSTOMER_NAME"
    
    # Show processing results if available
    if [ "$PROCESSING_RESULTS" != "null" ] && [ "$PROCESSING_RESULTS" != "[]" ]; then
        echo ""
        echo "Processing Results:"
        echo "$RESPONSE" | jq -r '.processing_results[] | "  - \(.agent_type | ascii_upcase) review completed at \(.created_at)"' 2>/dev/null || echo "  Processing results available"
    fi
    
    # Suggest next actions based on status
    echo ""
    if [ "$STATUS" = "uploaded" ]; then
        echo "Next steps - Process with agents:"
        echo "  ./test_process.sh $DOCUMENT_ID technical"
        echo "  ./test_process.sh $DOCUMENT_ID financial"
        echo "  ./test_process.sh $DOCUMENT_ID legal"
    elif [[ "$STATUS" == *"processed"* ]]; then
        echo "Document has been processed. You can:"
        echo "  - Process with other agent types"
        echo "  - Check the output files in S3"
        echo "  - Run ./test_list.sh to see all documents"
    fi
else
    echo ""
    echo "❌ Status check failed!"
    echo "Document may not exist or there was an API error"
    exit 1
fi

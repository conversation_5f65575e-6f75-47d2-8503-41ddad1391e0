output "document_status_table_name" {
  description = "Name of the document status DynamoDB table"
  value       = aws_dynamodb_table.document_status.name
}

output "document_status_table_arn" {
  description = "ARN of the document status DynamoDB table"
  value       = aws_dynamodb_table.document_status.arn
}

output "processing_results_table_name" {
  description = "Name of the processing results DynamoDB table"
  value       = aws_dynamodb_table.processing_results.name
}

output "processing_results_table_arn" {
  description = "ARN of the processing results DynamoDB table"
  value       = aws_dynamodb_table.processing_results.arn
}

output "slack_metadata_table_name" {
  description = "Name of the Slack scrape metadata DynamoDB table"
  value       = aws_dynamodb_table.slack_scrape_metadata.name
}

output "slack_metadata_table_arn" {
  description = "ARN of the Slack scrape metadata DynamoDB table"
  value       = aws_dynamodb_table.slack_scrape_metadata.arn
}

output "slack_channel_cache_table_name" {
  description = "Name of the Slack channel cache DynamoDB table"
  value       = aws_dynamodb_table.slack_channel_cache.name
}

output "slack_channel_cache_table_arn" {
  description = "ARN of the Slack channel cache DynamoDB table"
  value       = aws_dynamodb_table.slack_channel_cache.arn
}

output "slack_channel_reviews_table_name" {
  description = "Name of the Slack channel reviews DynamoDB table"
  value       = aws_dynamodb_table.slack_channel_reviews.name
}

output "slack_channel_reviews_table_arn" {
  description = "ARN of the Slack channel reviews DynamoDB table"
  value       = aws_dynamodb_table.slack_channel_reviews.arn
}
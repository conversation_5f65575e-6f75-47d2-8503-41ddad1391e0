# /reviews resource and method (for listing reviews)
resource "aws_api_gateway_resource" "reviews" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  parent_id   = aws_api_gateway_rest_api.sow_api.root_resource_id
  path_part   = "reviews"
}

resource "aws_api_gateway_method" "reviews_get" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.reviews.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "reviews_integration" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.reviews.id
  http_method = aws_api_gateway_method.reviews_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = var.list_handler_invoke_arn
}

resource "aws_lambda_permission" "reviews_permission" {
  statement_id  = "AllowAPIGatewayInvokeReviews"
  action        = "lambda:InvokeFunction"
  function_name = var.list_handler_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.sow_api.execution_arn}/*/*"
}

# CORS configuration for preflight requests - Reviews
resource "aws_api_gateway_method" "cors_reviews" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.reviews.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cors_reviews" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.reviews.id
  http_method = aws_api_gateway_method.cors_reviews.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "cors_reviews" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.reviews.id
  http_method = aws_api_gateway_method.cors_reviews.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_reviews" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.reviews.id
  http_method = aws_api_gateway_method.cors_reviews.http_method
  status_code = aws_api_gateway_method_response.cors_reviews.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "method.response.header.Access-Control-Allow-Methods" = "'GET,OPTIONS'"
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }
}
# API Gateway REST API
resource "aws_api_gateway_rest_api" "sow_api" {
  name        = "${var.name_prefix}${var.environment}-${var.project_name}-api"
  description = "SOW Reviewer API for ${var.environment} environment"

  tags = var.tags
}

# API Gateway Deployment
resource "aws_api_gateway_deployment" "sow_api_deployment" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.upload.id,
      aws_api_gateway_method.upload_post.id,
      aws_api_gateway_integration.upload_integration.id,
      aws_api_gateway_resource.process.id,
      aws_api_gateway_method.process_post.id,
      aws_api_gateway_integration.process_integration.id,
      aws_api_gateway_resource.status.id,
      aws_api_gateway_method.status_get.id,
      aws_api_gateway_integration.status_integration.id,
      aws_api_gateway_resource.documents.id,
      aws_api_gateway_method.documents_get.id,
      aws_api_gateway_integration.documents_integration.id,
      aws_api_gateway_resource.reviews.id,
      aws_api_gateway_method.reviews_get.id,
      aws_api_gateway_integration.reviews_integration.id,
      aws_api_gateway_method.cors_reviews.id,
      aws_api_gateway_integration.cors_reviews.id,
      aws_api_gateway_method_response.cors_reviews.id,
      aws_api_gateway_integration_response.cors_reviews.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

# API Gateway Stage
resource "aws_api_gateway_stage" "sow_api_stage" {
  deployment_id = aws_api_gateway_deployment.sow_api_deployment.id
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  stage_name    = var.environment

  tags = var.tags
}

# CloudWatch Log Groups for API Gateway
resource "aws_cloudwatch_log_group" "api_gateway_logs" {
  name              = "/aws/apigateway/${var.name_prefix}${var.environment}-${var.project_name}-api"
  retention_in_days = 14
  tags              = var.tags
}

resource "aws_cloudwatch_log_group" "api_gateway_access_logs" {
  name              = "/aws/apigateway/${var.name_prefix}${var.environment}-${var.project_name}-api-access"
  retention_in_days = 14
  tags              = var.tags
}

# CORS configuration for preflight requests - Upload
resource "aws_api_gateway_method" "cors_upload" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.upload.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cors_upload" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.upload.id
  http_method = aws_api_gateway_method.cors_upload.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "cors_upload" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.upload.id
  http_method = aws_api_gateway_method.cors_upload.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_upload" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.upload.id
  http_method = aws_api_gateway_method.cors_upload.http_method
  status_code = aws_api_gateway_method_response.cors_upload.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "method.response.header.Access-Control-Allow-Methods" = "'GET,OPTIONS,POST'"
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }
}

# CORS configuration for preflight requests - Process
resource "aws_api_gateway_method" "cors_process" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.process.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cors_process" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.process.id
  http_method = aws_api_gateway_method.cors_process.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "cors_process" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.process.id
  http_method = aws_api_gateway_method.cors_process.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_process" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.process.id
  http_method = aws_api_gateway_method.cors_process.http_method
  status_code = aws_api_gateway_method_response.cors_process.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "method.response.header.Access-Control-Allow-Methods" = "'GET,OPTIONS,POST'"
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }
}

# CORS configuration for preflight requests - Documents
resource "aws_api_gateway_method" "cors_documents" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.documents.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "cors_documents" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.documents.id
  http_method = aws_api_gateway_method.cors_documents.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "cors_documents" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.documents.id
  http_method = aws_api_gateway_method.cors_documents.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "cors_documents" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.documents.id
  http_method = aws_api_gateway_method.cors_documents.http_method
  status_code = aws_api_gateway_method_response.cors_documents.status_code

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "method.response.header.Access-Control-Allow-Methods" = "'GET,OPTIONS,POST'"
    "method.response.header.Access-Control-Allow-Origin"  = "'*'"
  }
}

# /upload resource and method
resource "aws_api_gateway_resource" "upload" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  parent_id   = aws_api_gateway_rest_api.sow_api.root_resource_id
  path_part   = "upload"
}

resource "aws_api_gateway_method" "upload_post" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.upload.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "upload_integration" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.upload.id
  http_method = aws_api_gateway_method.upload_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = var.upload_handler_invoke_arn
}

resource "aws_lambda_permission" "upload_permission" {
  statement_id  = "AllowAPIGatewayInvokeUpload"
  action        = "lambda:InvokeFunction"
  function_name = var.upload_handler_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.sow_api.execution_arn}/*/*"
}

# /process resource and method
resource "aws_api_gateway_resource" "process" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  parent_id   = aws_api_gateway_rest_api.sow_api.root_resource_id
  path_part   = "process"
}

resource "aws_api_gateway_method" "process_post" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.process.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "process_integration" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.process.id
  http_method = aws_api_gateway_method.process_post.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = var.agent_handler_invoke_arn
}

resource "aws_lambda_permission" "process_permission" {
  statement_id  = "AllowAPIGatewayInvokeProcess"
  action        = "lambda:InvokeFunction"
  function_name = var.agent_handler_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.sow_api.execution_arn}/*/*"
}

# /status/{document_id} resource and method
resource "aws_api_gateway_resource" "status" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  parent_id   = aws_api_gateway_rest_api.sow_api.root_resource_id
  path_part   = "status"
}

resource "aws_api_gateway_resource" "status_document_id" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  parent_id   = aws_api_gateway_resource.status.id
  path_part   = "{document_id}"
}

resource "aws_api_gateway_method" "status_get" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.status_document_id.id
  http_method   = "GET"
  authorization = "NONE"

  request_parameters = {
    "method.request.path.document_id" = true
  }
}

resource "aws_api_gateway_integration" "status_integration" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.status_document_id.id
  http_method = aws_api_gateway_method.status_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = var.status_handler_invoke_arn

  request_parameters = {
    "integration.request.path.document_id" = "method.request.path.document_id"
  }
}

resource "aws_lambda_permission" "status_permission" {
  statement_id  = "AllowAPIGatewayInvokeStatus"
  action        = "lambda:InvokeFunction"
  function_name = var.status_handler_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.sow_api.execution_arn}/*/*"
}

# /documents resource and method (for listing unprocessed documents)
resource "aws_api_gateway_resource" "documents" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  parent_id   = aws_api_gateway_rest_api.sow_api.root_resource_id
  path_part   = "documents"
}

resource "aws_api_gateway_method" "documents_get" {
  rest_api_id   = aws_api_gateway_rest_api.sow_api.id
  resource_id   = aws_api_gateway_resource.documents.id
  http_method   = "GET"
  authorization = "NONE"

  request_parameters = {
    "method.request.querystring.status" = false
    "method.request.querystring.limit"  = false
  }
}

resource "aws_api_gateway_integration" "documents_integration" {
  rest_api_id = aws_api_gateway_rest_api.sow_api.id
  resource_id = aws_api_gateway_resource.documents.id
  http_method = aws_api_gateway_method.documents_get.http_method

  integration_http_method = "POST"
  type                   = "AWS_PROXY"
  uri                    = var.list_handler_invoke_arn

  request_parameters = {
    "integration.request.querystring.status" = "method.request.querystring.status"
    "integration.request.querystring.limit"  = "method.request.querystring.limit"
  }
}

resource "aws_lambda_permission" "documents_permission" {
  statement_id  = "AllowAPIGatewayInvokeDocuments"
  action        = "lambda:InvokeFunction"
  function_name = var.list_handler_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.sow_api.execution_arn}/*/*"
}

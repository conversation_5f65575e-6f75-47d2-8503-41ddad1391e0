data "aws_caller_identity" "current" {}

# IAM assume role policy for Lambda
data "aws_iam_policy_document" "lambda_assume_role" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com", "edgelambda.amazonaws.com"]
    }
    actions = ["sts:AssumeRole"]
  }
}

# IAM permissions for Lambda functions
data "aws_iam_policy_document" "lambda_permissions" {
  statement {
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:ListBucket"
    ]
    resources = [
      var.main_bucket_arn,
      "${var.main_bucket_arn}/*"
    ]
  }

  # Access to Slack S3 bucket
  statement {
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:ListBucket"
    ]
    resources = [
      var.slack_bucket_arn,
      "${var.slack_bucket_arn}/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "dynamodb:GetItem",
      "dynamodb:PutItem",
      "dynamodb:UpdateItem",
      "dynamodb:DeleteItem",
      "dynamodb:Query",
      "dynamodb:Scan",
      "dynamodb:BatchWriteItem"
    ]
    resources = [
      var.document_status_table_arn,
      "${var.document_status_table_arn}/index/*",
      var.processing_results_table_arn,
      "${var.processing_results_table_arn}/index/*",
      var.slack_metadata_table_arn,
      var.slack_channel_cache_table_arn,
      var.slack_channel_reviews_table_arn
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "bedrock:InvokeModel",
      "bedrock:InvokeModelWithResponseStream",
      "bedrock:Converse",
      "bedrock:ConverseStream",
      "bedrock:UseInferenceProfile",
      "bedrock:GetFoundationModel",
      "bedrock:GetInferenceProfile"
    ]
    resources = ["*"]
  }

  # Explicit multi-region access for foundation models and inference profiles
  statement {
    effect = "Allow"
    actions = [
      "bedrock:InvokeModel",
      "bedrock:InvokeModelWithResponseStream",
      "bedrock:Converse",
      "bedrock:ConverseStream",
      "bedrock:UseInferenceProfile",
      "bedrock:GetFoundationModel",
      "bedrock:GetInferenceProfile"
    ]
    resources = [
      # Foundation models in target regions
      "arn:aws:bedrock:us-east-1::foundation-model/*",
      "arn:aws:bedrock:us-east-2::foundation-model/*",
      "arn:aws:bedrock:us-west-2::foundation-model/*",

      # Inference profiles in target regions for this account
      "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:inference-profile/*",
      "arn:aws:bedrock:us-east-2:${data.aws_caller_identity.current.account_id}:inference-profile/*",
      "arn:aws:bedrock:us-west-2:${data.aws_caller_identity.current.account_id}:inference-profile/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "bedrock:InvokeAgent",
      "bedrock-agent-runtime:InvokeAgent"
    ]
    resources = ["*"]
  }

  # Extra permissive scope for Bedrock agent resources, matching known-working config
  statement {
    effect = "Allow"
    actions = [
      "bedrock:*"
    ]
    resources = [
      # Current region
      "arn:aws:bedrock:${var.region}:${data.aws_caller_identity.current.account_id}:agent/*",
      "arn:aws:bedrock:${var.region}:${data.aws_caller_identity.current.account_id}:agent-alias/*",
      # Explicit regions required
      "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:agent/*",
      "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:agent-alias/*",
      "arn:aws:bedrock:us-east-2:${data.aws_caller_identity.current.account_id}:agent/*",
      "arn:aws:bedrock:us-east-2:${data.aws_caller_identity.current.account_id}:agent-alias/*",
      "arn:aws:bedrock:us-west-2:${data.aws_caller_identity.current.account_id}:agent/*",
      "arn:aws:bedrock:us-west-2:${data.aws_caller_identity.current.account_id}:agent-alias/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "bedrock:ListAgents",
      "bedrock:GetAgent",
      "bedrock:ListAgentAliases",
      "bedrock:GetAgentAlias"
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "sts:GetCallerIdentity"
    ]
    resources = ["*"]
  }

  # Full Bedrock permissions (development)
  statement {
    effect = "Allow"
    actions = [
      "bedrock:*",
      "bedrock-agent:*",
      "bedrock-agent-runtime:*"
    ]
    resources = ["*"]
  }

  # S3 Vectors permissions
  statement {
    effect = "Allow"
    actions = [
      "s3vectors:CreateVectorBucket",
      "s3vectors:DeleteVectorBucket",
      "s3vectors:GetVectorBucket",
      "s3vectors:ListVectorBuckets",
      "s3vectors:PutVectorBucketPolicy",
      "s3vectors:GetVectorBucketPolicy",
      "s3vectors:DeleteVectorBucketPolicy",
      "s3vectors:CreateIndex",
      "s3vectors:DeleteIndex",
      "s3vectors:GetIndex",
      "s3vectors:ListIndexes",
      "s3vectors:PutVectors",
      "s3vectors:GetVectors",
      "s3vectors:DeleteVectors",
      "s3vectors:QueryVectors",
      "s3vectors:ListVectors"
    ]
    resources = ["*"]
  }
}

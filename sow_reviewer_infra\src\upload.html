<!DOCTYPE html>
<html lang="en">
<head>
    <style>
        .navbar {
            width: 100vw;
            background: #232f3e;
            color: #fff;
            padding: 0.5rem 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            margin-bottom: 2rem;
        }
        .navbar-content {
            max-width: 1100px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        .navbar-title {
            font-weight: bold;
            font-size: 1.3em;
            margin-right: 2rem;
            letter-spacing: 1px;
        }
        .nav-link {
            color: #fff;
            text-decoration: none;
            margin-right: 1.5rem;
            font-size: 1.05em;
            padding: 0.3em 0.7em;
            border-radius: 4px;
            transition: background 0.15s;
        }
        .nav-link:hover, .nav-link.active {
            background: #4f8cff;
            color: #fff;
        }
    </style>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f4f6fb;
            min-height: 100vh;
            margin: 0;
        }
        .upload-container {
            background: #fff;
            padding: 2rem 3rem;
            border-radius: 12px;
            box-shadow: 0 2px 16px rgba(0,0,0,0.08);
            text-align: center;
        }
        .upload-container h2 {
            margin-bottom: 1rem;
        }
        .file-input {
            margin: 1rem 0;
        }
        .submit-btn {
            background: #4f8cff;
            color: #fff;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        .submit-btn:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-content">
            <span class="navbar-title">SOW Reviewer</span>
            <a href="index.html" class="nav-link">All Documents</a>
            <a href="upload.html" class="nav-link active">Upload Document</a>
        </div>
    </nav>
    <div style="display: flex; gap: 2rem; flex-wrap: wrap; justify-content: center; align-items: flex-start; min-height: 80vh;">
        <div class="upload-container" style="min-width:320px;">
            <h2>Upload Document</h2>
            <form id="upload-form">
                <input class="file-input" type="file" id="file-input" name="document" accept=".pdf,.doc,.docx,.txt" required><br>
                <input class="file-input" type="text" id="customer-name-upload" name="customer_name" placeholder="Customer Name" required><br>
                <button class="submit-btn" type="submit">Upload</button>
                <div id="document-id-display" style="margin-top:1em;font-size:0.95em;color:#333;"></div>
            </form>
            <div id="status"></div>
        </div>
        <!-- AI Agent Review Box -->
        <div class="upload-container" style="min-width:320px;">
            <h2>AI Agent Review</h2>
            <!-- Customer name search -->
            <div style="margin-bottom:1em;">
                <input class="file-input" type="text" id="search-customer-name" placeholder="Search by Customer Name">
                <button class="submit-btn" id="search-documents-btn" type="button" style="margin-top:0.5em;">Search</button>
            </div>
            <div id="document-search-results" style="margin-bottom:1em;"></div>
            <form id="review-form">
                <input class="file-input" type="text" id="document-id-input" name="document_id" placeholder="Document ID" required><br>
                <div class="review-type-container" style="margin-bottom:1rem;">
                    <label><input type="checkbox" name="review_type" value="Technical"> Technical Review</label>
                    <label style="margin-left:1em;"><input type="checkbox" name="review_type" value="Financial"> Financial Review</label>
                    <label style="margin-left:1em;"><input type="checkbox" name="review_type" value="Legal"> Legal Review</label>
                </div>
                <button class="submit-btn" type="submit">Run Review</button>
            </form>
            <div id="review-status"></div>
        </div>
    </div>
    </div>
    <script>
    // Document search by customer name
    document.getElementById('search-documents-btn').addEventListener('click', async function() {
        const customerName = document.getElementById('search-customer-name').value.trim();
        const resultsDiv = document.getElementById('document-search-results');
        resultsDiv.innerHTML = '';
        if (!customerName) {
            resultsDiv.textContent = 'Please enter a customer name.';
            return;
        }
        resultsDiv.textContent = 'Searching...';
        try {
            const endpoint = `https://gdy74fkaja.execute-api.us-east-1.amazonaws.com/dev/documents?customer_name=${encodeURIComponent(customerName)}`;
            const response = await fetch(endpoint, { method: 'GET' });
            const data = await response.json();
            if (response.ok && data.documents && data.documents.length > 0) {
                let html = '<b>Select a document:</b><ul style="list-style:none;padding-left:0;">';
                data.documents.forEach(doc => {
                    html += `<li style="margin-bottom:0.5em;">` +
                        `<button type='button' style='margin-right:0.5em;' onclick="document.getElementById('document-id-input').value='${doc.document_id}'">Select</button>` +
                        `<b>${doc.file_name}</b> <span style='font-size:0.9em;color:#555;'>(ID: ${doc.document_id})</span>` +
                        `</li>`;
                });
                html += '</ul>';
                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.textContent = 'No documents found for this customer.';
            }
        } catch (err) {
            resultsDiv.textContent = 'Error searching documents: ' + err.message;
        }
    });
    // Document upload logic
    document.getElementById('upload-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        const fileInput = document.getElementById('file-input');
        const customerNameInput = document.getElementById('customer-name-upload');
        const statusDiv = document.getElementById('status');
        const docIdDiv = document.getElementById('document-id-display');
        if (!fileInput.files.length) {
            statusDiv.textContent = 'Please select a file.';
            return;
        }
        if (!customerNameInput.value.trim()) {
            statusDiv.textContent = 'Please enter a customer name.';
            return;
        }
        const file = fileInput.files[0];
        const customerName = customerNameInput.value.trim();
        statusDiv.textContent = 'Reading file...';
        docIdDiv.textContent = '';
        // Read file as base64
        const reader = new FileReader();
        reader.onload = async function(event) {
            const base64String = event.target.result.split(',')[1]; // Remove data:...;base64, prefix
            statusDiv.textContent = 'Uploading...';
            try {
                const endpoint = 'https://gdy74fkaja.execute-api.us-east-1.amazonaws.com/dev/upload';
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        file_content: base64String,
                        file_name: file.name,
                        content_type: file.type,
                        customer_name: customerName
                    })
                });
                const data = await response.json();
                if (response.ok) {
                    statusDiv.textContent = 'Upload and processing complete!\n' + (data.message || '');
                    if (data.document_id) {
                        docIdDiv.textContent = 'Document ID: ' + data.document_id;
                        // Autofill document ID in review form for convenience
                        const docIdInput = document.getElementById('document-id-input');
                        if (docIdInput) docIdInput.value = data.document_id;
                    }
                } else {
                    statusDiv.textContent = 'Error: ' + (data.error || 'Unknown error');
                }
            } catch (err) {
                statusDiv.textContent = 'Error: ' + err.message;
            }
        };
        reader.onerror = function() {
            statusDiv.textContent = 'Failed to read file.';
        };
        reader.readAsDataURL(file);
    });
    // AI agent review logic
    document.getElementById('review-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        const documentId = document.getElementById('document-id-input').value.trim();
        const reviewTypes = Array.from(document.querySelectorAll('#review-form input[name="review_type"]:checked')).map(cb => cb.value);
        const reviewStatusDiv = document.getElementById('review-status');
        if (!documentId) {
            reviewStatusDiv.textContent = 'Please enter a document ID.';
            return;
        }
        if (reviewTypes.length === 0) {
            reviewStatusDiv.textContent = 'Please select at least one review type.';
            return;
        }
        reviewStatusDiv.textContent = 'Submitting review request...';
        let results = [];
        for (const type of reviewTypes) {
            // Map UI label to agent_type expected by backend
            let agentType = type.toLowerCase(); // 'Technical' -> 'technical'
            const endpoint = 'https://gdy74fkaja.execute-api.us-east-1.amazonaws.com/dev/process';
            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        document_id: documentId,
                        agent_type: agentType
                    })
                });
                const data = await response.json();
                if (response.ok) {
                    let uriMsg = '';
                    if (data.review_s3_uri) {
                        // Try to convert s3://bucket/key to a public S3 URL (works only if bucket/object is public)
                        const s3Match = data.review_s3_uri.match(/^s3:\/\/([^\/]+)\/(.+)$/);
                        if (s3Match) {
                            const bucket = s3Match[1];
                            const key = encodeURIComponent(s3Match[2]).replace(/%2F/g, '/');
                            const s3Url = `https://${bucket}.s3.amazonaws.com/${key}`;
                            uriMsg = `<br>S3 URI: <a href=\"${s3Url}\" target=\"_blank\">${s3Url}</a>`;
                        } else {
                            uriMsg = `<br>S3 URI: <code>${data.review_s3_uri}</code>`;
                        }
                    }
                    results.push(`${type} review: Success. ${(data.message || '')}${uriMsg}`);
                } else {
                    results.push(`${type} review: Error. ${(data.error || 'Unknown error')}`);
                }
            } catch (err) {
                results.push(`${type} review: Error. ${err.message}`);
            }
        }
        reviewStatusDiv.innerHTML = results.join('<br>');
    });
    </script>
</body>
</html>

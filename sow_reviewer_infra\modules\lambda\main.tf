# Install dependencies for agent operations handler (needs pypdf)
resource "null_resource" "install_agent_dependencies" {
  triggers = {
    lambda_hash = filesha256("${path.root}/src/agent_operations/lambda_function.py")
  }

  provisioner "local-exec" {
    interpreter = ["PowerShell", "-NoProfile", "-NonInteractive", "-ExecutionPolicy", "Bypass", "-Command"]
    working_dir = "${path.root}\\src\\agent_operations"

    command = <<-EOT
      $ErrorActionPreference = 'Stop'
      if (Test-Path -LiteralPath 'package') {
        Remove-Item -LiteralPath 'package' -Recurse -Force
      }
      New-Item -ItemType Directory -Path 'package' -Force | Out-Null
      if (Test-Path -LiteralPath 'requirements.txt') {
        & py -3 -m pip install -r requirements.txt -t .\package\
      }
      Copy-Item -LiteralPath 'lambda_function.py' -Destination (Join-Path -Path 'package' -ChildPath 'lambda_function.py') -Force
    EOT
  }

}

# Install dependencies for general operations handler (no external deps)
resource "null_resource" "install_general_dependencies" {
  triggers = {
    lambda_hash = filesha256("${path.root}/src/general_operations/lambda_function.py")
  }

  provisioner "local-exec" {
    interpreter = ["PowerShell", "-NoProfile", "-NonInteractive", "-ExecutionPolicy", "Bypass", "-Command"]
    working_dir = "${path.root}\\src\\general_operations"

    command = <<-EOT
      $ErrorActionPreference = 'Stop'
      if (Test-Path -LiteralPath 'package') {
        Remove-Item -LiteralPath 'package' -Recurse -Force
      }
      New-Item -ItemType Directory -Path 'package' -Force | Out-Null
      if (Test-Path -LiteralPath 'requirements.txt') {
        & py -3 -m pip install -r requirements.txt -t .\package\
      }
      Copy-Item -LiteralPath 'lambda_function.py' -Destination (Join-Path -Path 'package' -ChildPath 'lambda_function.py') -Force
    EOT
  }
}

# Create ZIP file for agent operations handler with dependencies
data "archive_file" "agent_operations_handler" {
  type        = "zip"
  source_dir  = "${path.root}/src/agent_operations/package"
  output_path = "${path.module}/agent_operations_handler.zip"
  
  depends_on = [null_resource.install_agent_dependencies]
}

# Create ZIP file for general operations handler
data "archive_file" "general_operations_handler" {
  type        = "zip"
  source_dir  = "${path.root}/src/general_operations/package"
  output_path = "${path.module}/general_operations_handler.zip"
  
  depends_on = [null_resource.install_general_dependencies]
}

# Package Slack scraper dependencies
resource "null_resource" "install_slack_dependencies" {
  triggers = {
    lambda_hash = filesha256("${path.root}/src/slack_scraper/lambda_function.py")
  }

  provisioner "local-exec" {
    interpreter = ["PowerShell", "-NoProfile", "-NonInteractive", "-ExecutionPolicy", "Bypass", "-Command"]
    working_dir = "${path.root}\\src\\slack_scraper"

    command = <<-EOT
      $ErrorActionPreference = 'Stop'
      if (Test-Path -LiteralPath 'package') {
        Remove-Item -LiteralPath 'package' -Recurse -Force
      }
      New-Item -ItemType Directory -Path 'package' -Force | Out-Null
      if (Test-Path -LiteralPath 'requirements.txt') {
        & py -3 -m pip install -r requirements.txt -t .\package\
      }
      Copy-Item -LiteralPath 'lambda_function.py' -Destination (Join-Path -Path 'package' -ChildPath 'lambda_function.py') -Force
    EOT
  }

}

data "archive_file" "slack_scraper_handler" {
  type        = "zip"
  source_dir  = "${path.root}/src/slack_scraper/package"
  output_path = "${path.module}/slack_scraper_handler.zip"
  
  depends_on = [null_resource.install_slack_dependencies]
}

# Package Slack channel reviewer dependencies
resource "null_resource" "install_slack_reviewer_dependencies" {
  triggers = {
    lambda_hash = filesha256("${path.root}/src/slack_channel_reviewer/lambda_function.py")
  }

  provisioner "local-exec" {
    interpreter = ["PowerShell", "-NoProfile", "-NonInteractive", "-ExecutionPolicy", "Bypass", "-Command"]
    working_dir = "${path.root}\\src\\slack_channel_reviewer"

    command = <<-EOT
      $ErrorActionPreference = 'Stop'
      if (Test-Path -LiteralPath 'package') {
        Remove-Item -LiteralPath 'package' -Recurse -Force
      }
      New-Item -ItemType Directory -Path 'package' -Force | Out-Null
      if (Test-Path -LiteralPath 'requirements.txt') {
        & py -3 -m pip install -r requirements.txt -t .\package\
      }
      Copy-Item -LiteralPath 'lambda_function.py' -Destination (Join-Path -Path 'package' -ChildPath 'lambda_function.py') -Force
    EOT
  }
  

}

data "archive_file" "slack_channel_reviewer_handler" {
  type        = "zip"
  source_dir  = "${path.root}/src/slack_channel_reviewer/package"
  output_path = "${path.module}/slack_channel_reviewer_handler.zip"
  
  depends_on = [null_resource.install_slack_reviewer_dependencies]
}

# IAM Role for Lambda functions
resource "aws_iam_role" "lambda_role" {
  name_prefix = "${var.name_prefix}lambda-role-"
  assume_role_policy = data.aws_iam_policy_document.lambda_assume_role.json

  tags = var.tags
}

resource "aws_iam_role_policy" "lambda_policy" {
  name_prefix = "${var.name_prefix}lambda-policy-"
  policy = data.aws_iam_policy_document.lambda_permissions.json
  role   = aws_iam_role.lambda_role.id
}

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
  role       = aws_iam_role.lambda_role.name
}

# CloudWatch Log Groups for Lambda functions
resource "aws_cloudwatch_log_group" "agent_operations_logs" {
  name              = "/aws/lambda/${var.name_prefix}${var.project_name}-agent-ops"
  retention_in_days = var.log_retention_in_days
  tags              = var.tags
}

resource "aws_cloudwatch_log_group" "general_operations_logs" {
  name              = "/aws/lambda/${var.name_prefix}${var.project_name}-general-ops"
  retention_in_days = var.log_retention_in_days
  tags              = var.tags
}

resource "aws_cloudwatch_log_group" "slack_scraper_logs" {
  name              = "/aws/lambda/${var.name_prefix}${var.project_name}-slack-scraper"
  retention_in_days = var.log_retention_in_days
  tags              = var.tags
}

resource "aws_cloudwatch_log_group" "slack_channel_reviewer_logs" {
  name              = "/aws/lambda/${var.name_prefix}${var.project_name}-slack-channel-reviewer"
  retention_in_days = var.log_retention_in_days
  tags              = var.tags
}

# CloudWatch Log Group for EventBridge rule


# Agent Operations Lambda - Handles all agent-related processing
resource "aws_lambda_function" "agent_operations_handler" {
  filename         = data.archive_file.agent_operations_handler.output_path
  function_name    = "${var.name_prefix}${var.project_name}-agent-ops"
  role            = aws_iam_role.lambda_role.arn
  handler         = "lambda_function.lambda_handler"
  source_code_hash = data.archive_file.agent_operations_handler.output_base64sha256
  timeout         = 900
  memory_size     = 1024
  runtime         = "python3.11"

  environment {
    variables = {
      MAIN_BUCKET_NAME         = var.main_bucket_name
      DOCUMENT_STATUS_TABLE    = var.document_status_table_name
      PROCESSING_RESULTS_TABLE = var.processing_results_table_name
      SLACK_CHANNEL_REVIEWS_TABLE = var.slack_channel_reviews_table_name
      ENVIRONMENT             = var.environment
    }
  }

  tags = var.tags
}

# General Operations Lambda - Handles upload, status, and list operations
resource "aws_lambda_function" "general_operations_handler" {
  filename         = data.archive_file.general_operations_handler.output_path
  function_name    = "${var.name_prefix}${var.project_name}-general-ops"
  role            = aws_iam_role.lambda_role.arn
  handler         = "lambda_function.lambda_handler"
  source_code_hash = data.archive_file.general_operations_handler.output_base64sha256
  timeout         = 60
  memory_size     = 512
  runtime         = "python3.11"

  environment {
    variables = {
      MAIN_BUCKET_NAME         = var.main_bucket_name
      DOCUMENT_STATUS_TABLE    = var.document_status_table_name
      PROCESSING_RESULTS_TABLE = var.processing_results_table_name
      ENVIRONMENT             = var.environment
    }
  }

  tags = var.tags
}

# Slack Scraper Lambda - Scrapes Slack channels daily and stores data in S3
resource "aws_lambda_function" "slack_scraper" {
  filename         = data.archive_file.slack_scraper_handler.output_path
  function_name    = "${var.name_prefix}${var.project_name}-slack-scraper"
  role             = aws_iam_role.lambda_role.arn
  handler          = "lambda_function.lambda_handler"
  source_code_hash = data.archive_file.slack_scraper_handler.output_base64sha256
  timeout          = 900
  memory_size      = 1024
  runtime          = "python3.11"

  environment {
    variables = {
      SLACK_BOT_TOKEN         = var.slack_bot_token
      SLACK_APP_TOKEN         = var.slack_app_token
      SLACK_WORKSPACE_ID      = var.slack_workspace_id
      SLACK_S3_BUCKET         = var.slack_bucket_name
      SLACK_METADATA_TABLE    = var.slack_metadata_table_name
      SLACK_CHANNEL_CACHE_TABLE = var.slack_channel_cache_table_name
      ENVIRONMENT             = var.environment
    }
  }

  tags = var.tags
}

# Slack Channel Reviewer Lambda - Analyzes Slack channel content using Claude Sonnet 4
resource "aws_lambda_function" "slack_channel_reviewer" {
  filename         = data.archive_file.slack_channel_reviewer_handler.output_path
  function_name    = "${var.name_prefix}${var.project_name}-slack-channel-reviewer"
  role             = aws_iam_role.lambda_role.arn
  handler          = "lambda_function.lambda_handler"
  source_code_hash = data.archive_file.slack_channel_reviewer_handler.output_base64sha256
  timeout          = 900
  memory_size      = 2048
  runtime          = "python3.11"

  environment {
    variables = {
      SLACK_S3_BUCKET         = var.slack_bucket_name
      SLACK_CHANNEL_REVIEWS_TABLE = var.slack_channel_reviews_table_name
      SLACK_WORKSPACE_ID      = var.slack_workspace_id
      S3_VECTORS_BUCKET       = var.s3_vectors_bucket_name
      S3_VECTORS_INDEX_NAME   = var.s3_vectors_index_name
      ENVIRONMENT             = var.environment
    }
  }

  tags = var.tags
}

resource "aws_cloudwatch_event_rule" "slack_scraper_daily" {
  name                = "${var.name_prefix}${var.project_name}-slack-scraper-daily"
  schedule_expression = "rate(1 day)"
  tags                = var.tags
}

resource "aws_cloudwatch_event_target" "slack_scraper_target" {
  rule      = aws_cloudwatch_event_rule.slack_scraper_daily.name
  target_id = "slack-scraper"
  arn       = aws_lambda_function.slack_scraper.arn
}

resource "aws_lambda_permission" "allow_events_slack_scraper" {
  statement_id  = "AllowExecutionFromEventBridgeSlackScraper"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.slack_scraper.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.slack_scraper_daily.arn
}

import json
import boto3
import os
import re
from datetime import datetime
from typing import Dict, List, Any
import logging

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
s3_client = boto3.client('s3')
s3vectors_client = boto3.client('s3vectors')
dynamodb_client = boto3.client('dynamodb')
bedrock_client = boto3.client('bedrock-runtime')

def lambda_handler(event, context):
    """
    Lambda function to review Slack channel content using Claude Sonnet 4.
    
    Expected event format:
    {
        "channel_id": "C1234567890",
        "channel_name": "project-discussions"
    }
    """
    try:
        # Extract parameters from event
        channel_id = event.get('channel_id')
        channel_name = event.get('channel_name', channel_id)
        
        if not channel_id:
            raise ValueError("channel_id is required in the event")
        
        logger.info(f"Starting review for channel: {channel_id} ({channel_name})")
        start_time = datetime.now()
        
        # Get environment variables
        slack_bucket = os.environ['SLACK_S3_BUCKET']
        reviews_table = os.environ['SLACK_CHANNEL_REVIEWS_TABLE']
        workspace_id = os.environ.get('SLACK_WORKSPACE_ID', 'default')
        inference_profile_arn = os.environ.get('BEDROCK_INFERENCE_PROFILE_ARN')
        
        logger.info(
            json.dumps({
                'env': {
                    'SLACK_S3_BUCKET': slack_bucket,
                    'SLACK_CHANNEL_REVIEWS_TABLE': reviews_table,
                    'SLACK_WORKSPACE_ID': workspace_id,
                    'BEDROCK_INFERENCE_PROFILE_ARN_present': bool(inference_profile_arn)
                },
                'event_keys': list(event.keys())
            })
        )
        
        # Read and combine all text files for the channel
        combined_content = read_channel_files(slack_bucket, channel_id)
        
        if not combined_content:
            logger.warning(f"No content found for channel {channel_id}")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': f'No content found for channel {channel_id}',
                    'channel_id': channel_id,
                    'channel_name': channel_name
                })
            }
        
        logger.info(
            json.dumps({
                's3_read_summary': {
                    'files_found': combined_content.get('total_files', 0),
                    'total_content_length': combined_content.get('total_content_length', 0)
                }
            })
        )
        
        # Analyze content with Claude Sonnet 4 (SOW-focused analysis)
        analysis_results = analyze_with_claude(combined_content['text'], channel_name)
        
        # Summarize channel content with Claude Sonnet 4 (project overview)
        summary_results = summarize_channel_content(combined_content['text'], channel_name)
        
        # Set review date
        review_date = datetime.now().isoformat()
        
        # Vectorize summary data and find similar channels
        vector_results = store_vector_data(channel_id, channel_name, summary_results['raw_summary'], 
                                         combined_content, review_date)
        
        # Store results in DynamoDB
        store_results(dynamodb_client, reviews_table, channel_id, channel_name, 
                     combined_content, analysis_results, summary_results, vector_results, review_date, start_time)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        logger.info(
            json.dumps({
                'analysis': {
                    'parsed_counts': {
                        'good_points': len(analysis_results.get('parsed_results', {}).get('good_points', [])),
                        'bad_points': len(analysis_results.get('parsed_results', {}).get('bad_points', [])),
                        'recommendations': len(analysis_results.get('parsed_results', {}).get('recommendations', []))
                    }
                },
                'timing': {
                    'total_seconds': processing_time
                }
            })
        )
        
        logger.info(f"Successfully completed review for channel {channel_id} in {processing_time:.2f} seconds")
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Channel review, summarization, and vectorization completed successfully',
                'channel_id': channel_id,
                'channel_name': channel_name,
                'file_count': len(combined_content['files']),
                'total_content_length': len(combined_content['text']),
                'processing_time': processing_time,
                'review_date': review_date,
                'analysis_completed': True,
                'summary_completed': True,
                'vectorization_completed': vector_results.get('vectorization_success', False),
                'similar_channels_found': len(vector_results.get('similar_channels', [])),
                'embeddings_generated': vector_results.get('embeddings_generated', 0)
            })
        }
        
    except Exception as e:
        logger.error(f"Error in lambda_handler: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e),
                'channel_id': event.get('channel_id', 'unknown')
            })
        }

def read_channel_files(bucket_name: str, channel_id: str) -> Dict[str, Any]:
    """
    Read all text files for a specific channel from S3 and combine them chronologically.
    The Slack scraper writes files to: slack/{workspace_id}/channels/{channel_id}_{channel_name}/date={date_prefix}/{timestamp}.txt
    """
    try:
        # Get workspace ID from environment
        workspace_id = os.environ.get('SLACK_WORKSPACE_ID', 'default')
        
        # List all objects in the channel folder - the path structure is:
        # slack/{workspace_id}/channels/{channel_id}_{channel_name}/
        prefix = f"slack/{workspace_id}/channels/{channel_id}_"
        logger.info(json.dumps({'s3_list_prefix': prefix}))
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=prefix
        )
        
        if 'Contents' not in response:
            logger.warning(f"No files found with prefix {prefix}")
            return None
        
        # Filter for .txt files and extract timestamps
        txt_files = []
        for obj in response['Contents']:
            key = obj['Key']
            if key.endswith('.txt'):
                # Extract timestamp from the filename (last part of the path)
                # Path format: slack/{workspace_id}/channels/{channel_id}_{channel_name}/date={date_prefix}/{timestamp}.txt
                filename = key.split('/')[-1]
                if filename.endswith('.txt'):
                    try:
                        # Extract timestamp from filename (should be a Unix timestamp)
                        timestamp_str = filename.replace('.txt', '')
                        timestamp = datetime.fromtimestamp(int(timestamp_str))
                        txt_files.append({
                            'key': key,
                            'timestamp': timestamp,
                            'size': obj['Size']
                        })
                    except (ValueError, TypeError):
                        logger.warning(f"Could not parse timestamp from filename: {key}")
                        # Use file creation time as fallback
                        txt_files.append({
                            'key': key,
                            'timestamp': obj['LastModified'].replace(tzinfo=None),
                            'size': obj['Size']
                        })
        
        if not txt_files:
            logger.warning(f"No .txt files found with prefix {prefix}")
            return None
        
        # Sort files by timestamp (oldest first)
        txt_files.sort(key=lambda x: x['timestamp'])
        
        # Read and combine content
        combined_text = ""
        file_info = []
        total_bytes = 0
        
        for file_data in txt_files:
            try:
                response = s3_client.get_object(
                    Bucket=bucket_name,
                    Key=file_data['key']
                )
                
                content = response['Body'].read().decode('utf-8')
                total_bytes += len(content)
                combined_text += f"\n\n--- File: {file_data['key']} ({file_data['timestamp'].isoformat()}) ---\n\n"
                combined_text += content
                
                file_info.append({
                    'filename': file_data['key'],
                    'timestamp': file_data['timestamp'].isoformat(),
                    'size': file_data['size'],
                    'content_length': len(content)
                })
                
                logger.info(f"Read file: {file_data['key']} ({len(content)} characters)")
                
            except Exception as e:
                logger.error(f"Error reading file {file_data['key']}: {str(e)}")
                continue
        
        logger.info(json.dumps({'s3_read_totals': {'files': len(file_info), 'bytes': total_bytes}}))
        
        return {
            'text': combined_text,
            'files': file_info,
            'total_files': len(file_info),
            'total_content_length': len(combined_text)
        }
        
    except Exception as e:
        logger.error(f"Error reading channel files: {str(e)}", exc_info=True)
        raise

def analyze_with_claude(content: str, channel_name: str) -> Dict[str, Any]:
    """
    Analyze the combined Slack content using Claude Sonnet (via Bedrock Converse).
    """
    try:
        # Build SOW-focused, section-based prompt
        prompt = f"""You are an expert project analyst preparing inputs to improve future SOW (Statement of Work) documents.

Review the Slack channel content for "{channel_name}" and produce a SECTION-BASED project retrospective that will feed directly into a future SOW. For each SOW section below, list:
1) GOOD (what worked well),
2) BAD (issues, gaps, or risks),
3) RECOMMENDATIONS (clear, actionable, specific to that section).

Required SOW sections:
- Objectives & Success Criteria
- Scope & Out-of-Scope
- Deliverables & Acceptance Criteria
- Functional & Non-Functional Requirements
- Architecture & Design (services, components, patterns)
- APIs & Integrations (including third-party providers)
- Data & Storage (schemas, throughput, retention, migrations)
- Security, Privacy & Compliance
- Performance, Reliability & SLAs
- Environments (dev/staging/prod) & Configuration
- Deployment, CI/CD & Release Management
- Observability (logging, metrics, tracing, alerting)
- Risks, Assumptions & Dependencies
- Timeline, Milestones & Sequencing
- Team, Skills & Communication
- Change Management & Governance
- Budget, Effort & Cost Drivers

Additionally include:
- CROSS-CUTTING THEMES: recurring patterns across the project
- TOP RISKS (prioritized, with mitigation steps)
- CRITICAL ACTIONS before project start (checklist for the SOW)

Format strictly as:
**GOOD POINTS:**
<bulleted items grouped under sub-headings for each SOW section>

**BAD POINTS:**
<bulleted items grouped under sub-headings for each SOW section>

**RECOMMENDATIONS:**
<bulleted items grouped under sub-headings for each SOW section>

Keep bullets concise, concrete, and implementation-oriented. Refer to specific services, components, or deployments where possible (e.g., Cognito, API Gateway, Lambda, DynamoDB, VPC, Amadeus API, Weather API, model endpoints, etc.).

SLACK CHANNEL CONTENT:
{content}
"""

        # Always use Sonnet 4 via Bedrock Converse
        call_started = datetime.now()
        logger.info(json.dumps({'bedrock_call': {'mode': 'direct_model', 'modelId': 'us.anthropic.claude-sonnet-4-20250514-v1:0'}}))
        response = bedrock_client.converse(
            modelId='us.anthropic.claude-sonnet-4-20250514-v1:0',
            messages=[
                {
                    'role': 'user',
                    'content': [
                        {
                            'text': prompt
                        }
                        
                    ]
                }
            ],
            inferenceConfig={
                'maxTokens': 4000,
                'temperature': 0.2
            }
        )
        call_latency = (datetime.now() - call_started).total_seconds()
        logger.info(json.dumps({'bedrock_call_metrics': {'latency_seconds': call_latency}}))
        
        # Extract text from Converse output
        output_message = response.get('output', {}).get('message', {})
        content_blocks = output_message.get('content', [])
        claude_response_parts = []
        for block in content_blocks:
            text_part = block.get('text')
            if text_part:
                claude_response_parts.append(text_part)
        claude_response = "".join(claude_response_parts) if claude_response_parts else json.dumps(response)
        
        # Parse the response to extract structured data
        # analysis_results = parse_claude_response(claude_response)
        
        return {
            'raw_response': claude_response
        }
        
    except Exception as e:
        logger.error(f"Error analyzing with Claude: {str(e)}", exc_info=True)
        # Return a fallback response
        return {
            'raw_response': f"Error during analysis: {str(e)}",
        }


def summarize_channel_content(content: str, channel_name: str) -> Dict[str, Any]:
    """
    Summarize the combined Slack content focusing on architecture, services, workflows, 
    project title/purpose, and patterns using Claude Sonnet.
    """
    try:
        # Build summary-focused prompt
        prompt = f"""You are an expert technical analyst reviewing Slack channel content for project documentation purposes.

Analyze the Slack channel content for "{channel_name}" and provide a comprehensive technical summary focusing on:

**PROJECT OVERVIEW:**
- Project Title/Name (infer from context if not explicitly stated)
- Project Purpose & Business Objectives
- Key Stakeholders & Team Members mentioned
- Project Timeline & Important Milestones

**TECHNICAL ARCHITECTURE:**
- System Architecture Overview (high-level design patterns)
- Core Services & Components (AWS services, databases, APIs, etc.)
- Integration Points & Dependencies
- Technology Stack (programming languages, frameworks, tools)

**SERVICES & INFRASTRUCTURE:**
- AWS Services utilized (Lambda, DynamoDB, S3, API Gateway, etc.)
- Third-party Services & APIs
- Database Design & Data Models
- Authentication & Authorization mechanisms

**WORKFLOWS & PROCESSES:**
- Key Business Workflows
- Development Workflows (CI/CD, deployment processes)
- Data Processing Flows
- User Journey & Experience Flows

**PATTERNS & BEST PRACTICES:**
- Design Patterns implemented
- Security Patterns & Considerations
- Performance Optimization Patterns
- Code Organization & Structure Patterns

**CHALLENGES & SOLUTIONS:**
- Technical Challenges encountered
- Solutions implemented
- Lessons Learned
- Performance & Scalability considerations

Format the response as structured sections with clear headings and bullet points. Be specific about technologies, services, and implementation details mentioned in the discussions.

SLACK CHANNEL CONTENT:
{content}
"""

        # Use Sonnet 4 via Bedrock Converse
        call_started = datetime.now()
        logger.info(json.dumps({'bedrock_summary_call': {'mode': 'direct_model', 'modelId': 'us.anthropic.claude-sonnet-4-20250514-v1:0'}}))
        response = bedrock_client.converse(
            modelId='us.anthropic.claude-sonnet-4-20250514-v1:0',
            messages=[
                {
                    'role': 'user',
                    'content': [
                        {
                            'text': prompt
                        }
                    ]
                }
            ],
            inferenceConfig={
                'maxTokens': 4000,
                'temperature': 0.1
            }
        )
        call_latency = (datetime.now() - call_started).total_seconds()
        logger.info(json.dumps({'bedrock_summary_call_metrics': {'latency_seconds': call_latency}}))
        
        # Extract text from Converse output
        output_message = response.get('output', {}).get('message', {})
        content_blocks = output_message.get('content', [])
        claude_response_parts = []
        for block in content_blocks:
            text_part = block.get('text')
            if text_part:
                claude_response_parts.append(text_part)
        claude_response = "".join(claude_response_parts) if claude_response_parts else json.dumps(response)
        
        return {
            'raw_summary': claude_response
        }
        
    except Exception as e:
        logger.error(f"Error summarizing with Claude: {str(e)}", exc_info=True)
        # Return a fallback response
        return {
            'raw_summary': f"Error during summarization: {str(e)}",
        }


def generate_embeddings(text: str) -> List[float]:
    """
    Generate embeddings for text using Bedrock Titan embedding model.
    """
    try:
        # Use Amazon Titan Embeddings V2 model
        call_started = datetime.now()
        logger.info(json.dumps({'bedrock_embedding_call': {'mode': 'titan_embeddings', 'modelId': 'amazon.titan-embed-text-v2:0'}}))
        
        response = bedrock_client.invoke_model(
            modelId='amazon.titan-embed-text-v2:0',
            body=json.dumps({
                'inputText': text,
                'dimensions': 1024,  # Using 1024 dimensions for better performance
                'normalize': True
            })
        )
        
        call_latency = (datetime.now() - call_started).total_seconds()
        logger.info(json.dumps({'bedrock_embedding_call_metrics': {'latency_seconds': call_latency}}))
        
        response_body = json.loads(response['body'].read())
        embeddings = response_body.get('embedding', [])
        
        if not embeddings:
            raise ValueError("No embeddings returned from Bedrock")
            
        return embeddings
        
    except Exception as e:
        logger.error(f"Error generating embeddings: {str(e)}", exc_info=True)
        # Return a zero vector as fallback
        return [0.0] * 1024


def setup_s3_vectors_infrastructure(bucket_name: str, index_name: str):
    """
    Set up S3 Vectors bucket and index for storing channel summaries and embeddings.
    """
    try:
        # Check if vector bucket exists, create if not
        try:
            s3vectors_client.get_vector_bucket(vectorBucketName=bucket_name)
            logger.info(f"S3 Vector bucket {bucket_name} already exists")
        except Exception as e:
            error_code = getattr(e, 'response', {}).get('Error', {}).get('Code', str(e))
            if 'NotFoundException' in str(type(e)) or 'NoSuchVectorBucket' in error_code:
                # Create vector bucket
                s3vectors_client.create_vector_bucket(vectorBucketName=bucket_name)
                logger.info(f"Created S3 Vector bucket: {bucket_name}")
            else:
                logger.error(f"Unexpected error checking vector bucket: {str(e)}")
                raise
        
        # Check if index exists, create if not
        try:
            s3vectors_client.get_index(vectorBucketName=bucket_name, indexName=index_name)
            logger.info(f"S3 Vector index {index_name} already exists")
        except Exception as e:
            error_code = getattr(e, 'response', {}).get('Error', {}).get('Code', str(e))
            if 'NotFoundException' in str(type(e)) or 'NoSuchIndex' in error_code:
                # Create vector index with 1024 dimensions for Titan embeddings
                s3vectors_client.create_index(
                    vectorBucketName=bucket_name,
                    indexName=index_name,
                    dataType='float32',
                    dimension=1024,
                    distanceMetric='cosine'
                )
                logger.info(f"Created S3 Vector index: {index_name}")
            else:
                logger.error(f"Unexpected error checking vector index: {str(e)}")
                raise
                
    except Exception as e:
        logger.error(f"Error setting up S3 Vectors infrastructure: {str(e)}", exc_info=True)
        raise


def extract_summary_metadata(summary_text: str) -> Dict[str, str]:
    """
    Extract structured metadata from the summary text using simple parsing.
    """
    try:
        metadata = {
            'project_title': 'Unknown',
            'technology_stack': '',
            'aws_services': '',
            'workflows': '',
            'patterns': ''
        }
        
        # Extract project title (look for common patterns)
        title_patterns = [
            r'Project Title[:/]\s*([^\n]+)',
            r'Project Name[:/]\s*([^\n]+)',
            r'Project[:/]\s*([^\n]+)',
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, summary_text, re.IGNORECASE)
            if match:
                metadata['project_title'] = match.group(1).strip()
                break
        
        # Extract technology stack
        tech_section = re.search(r'technology stack.*?(?=\n\*\*|\nEND|\Z)', summary_text, re.IGNORECASE | re.DOTALL)
        if tech_section:
            metadata['technology_stack'] = tech_section.group(0)[:500]  # Limit length
        
        # Extract AWS services
        aws_section = re.search(r'aws services.*?(?=\n\*\*|\nEND|\Z)', summary_text, re.IGNORECASE | re.DOTALL)
        if aws_section:
            metadata['aws_services'] = aws_section.group(0)[:500]
        
        # Extract workflows
        workflow_section = re.search(r'workflows.*?(?=\n\*\*|\nEND|\Z)', summary_text, re.IGNORECASE | re.DOTALL)
        if workflow_section:
            metadata['workflows'] = workflow_section.group(0)[:500]
        
        # Extract patterns
        pattern_section = re.search(r'patterns.*?(?=\n\*\*|\nEND|\Z)', summary_text, re.IGNORECASE | re.DOTALL)
        if pattern_section:
            metadata['patterns'] = pattern_section.group(0)[:500]
        
        return metadata
        
    except Exception as e:
        logger.error(f"Error extracting summary metadata: {str(e)}")
        return {
            'project_title': 'Unknown',
            'technology_stack': '',
            'aws_services': '',
            'workflows': '',
            'patterns': ''
        }


def store_vector_data(channel_id: str, channel_name: str, summary_text: str, 
                     content_data: Dict[str, Any], review_date: str) -> Dict[str, Any]:
    """
    Store vectorized summary data in S3 Vectors and return similarity results.
    """
    try:
        # Get S3 Vectors configuration from environment variables
        vector_bucket_name = os.environ.get('S3_VECTORS_BUCKET', f"slack-vectors-{os.environ.get('SLACK_WORKSPACE_ID', 'default')}")
        index_name = os.environ.get('S3_VECTORS_INDEX_NAME', 'channel-summaries')
        
        # Set up S3 Vectors infrastructure
        setup_s3_vectors_infrastructure(vector_bucket_name, index_name)
        
        # Generate embeddings for the summary
        logger.info("Generating embeddings for summary text")
        embeddings = generate_embeddings(summary_text)
        
        # Extract metadata from summary
        metadata = extract_summary_metadata(summary_text)
        
        # Find similar channels before adding the new one
        similar_channels = find_similar_channels_s3(vector_bucket_name, index_name, embeddings, channel_id)
        
        # Prepare vector metadata
        vector_metadata = {
            'channel_name': channel_name,
            'project_title': metadata['project_title'],
            'technology_stack': metadata['technology_stack'][:200],  # Limit metadata size
            'aws_services': metadata['aws_services'][:200],
            'workflows': metadata['workflows'][:200],
            'patterns': metadata['patterns'][:200],
            'review_date': review_date,
            'file_count': str(content_data['total_files']),
            'content_length': str(content_data['total_content_length'])
        }
        
        # Store vector in S3 Vectors
        s3vectors_client.put_vectors(
            vectorBucketName=vector_bucket_name,
            indexName=index_name,
            vectors=[
                {
                    'key': channel_id,
                    'data': {
                        'float32': [float(x) for x in embeddings]
                    },
                    'metadata': vector_metadata
                }
            ]
        )
        
        logger.info(f"Successfully stored vector data for channel {channel_id} in S3 Vectors")
        
        return {
            'vectorization_success': True,
            'similar_channels': similar_channels,
            'embeddings_generated': len(embeddings),
            'metadata_extracted': len([k for k, v in metadata.items() if v]),
            'vector_bucket': vector_bucket_name,
            'index_name': index_name
        }
        
    except Exception as e:
        logger.error(f"Error storing vector data: {str(e)}", exc_info=True)
        return {
            'vectorization_success': False,
            'error': str(e),
            'similar_channels': []
        }


def find_similar_channels_s3(vector_bucket_name: str, index_name: str, query_embeddings: List[float], 
                            exclude_channel_id: str, limit: int = 5) -> List[Dict[str, Any]]:
    """
    Find similar channels using S3 Vectors similarity search.
    """
    try:
        # Perform similarity search using S3 Vectors
        response = s3vectors_client.query_vectors(
            vectorBucketName=vector_bucket_name,
            indexName=index_name,
            queryVector={
                'float32': [float(x) for x in query_embeddings]
            },
            topK=limit + 1,
            returnMetadata=True,
            returnDistance=True
        )
        
        # Format results and filter out current channel
        similar_channels = []
        for result in (response.get('results') or response.get('vectors') or []):
            channel_id = result.get('key', result.get('Key', ''))
            
            # Skip the current channel
            if channel_id == exclude_channel_id:
                continue
                
            metadata = result.get('metadata', result.get('Metadata', {}))
            # Some APIs return distance; convert to similarity if needed
            distance = result.get('distance', result.get('Distance'))
            if distance is not None:
                try:
                    similarity_score = 1.0 - float(distance)
                except Exception:
                    similarity_score = 0.0
            else:
                similarity_score = result.get('similarityScore', result.get('SimilarityScore', 0.0))
            
            # Format technology stack for display
            tech_stack = metadata.get('technology_stack', '')
            if len(tech_stack) > 100:
                tech_stack = tech_stack[:100] + '...'
            
            similar_channels.append({
                'channel_id': channel_id,
                'channel_name': metadata.get('channel_name', 'Unknown'),
                'project_title': metadata.get('project_title', 'Unknown'),
                'similarity_score': float(similarity_score),
                'review_date': metadata.get('review_date', ''),
                'technology_stack': tech_stack,
                'aws_services': metadata.get('aws_services', '')[:100] + '...' if len(metadata.get('aws_services', '')) > 100 else metadata.get('aws_services', ''),
                'workflows': metadata.get('workflows', '')[:100] + '...' if len(metadata.get('workflows', '')) > 100 else metadata.get('workflows', '')
            })
        
        # Sort by similarity score (higher score = more similar)
        similar_channels.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        # Return top results
        return similar_channels[:limit]
        
    except Exception as e:
        logger.error(f"Error finding similar channels: {str(e)}", exc_info=True)
        return []


def store_results(dynamodb_client, table_name: str, channel_id: str, channel_name: str,
                 content_data: Dict[str, Any], analysis_results: Dict[str, Any], 
                 summary_results: Dict[str, Any], vector_results: Dict[str, Any], 
                 review_date: str, start_time: datetime):
    """
    Store the analysis, summary, and vector similarity results in DynamoDB.
    """
    try:
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Prepare similar channels data for storage
        similar_channels_json = json.dumps(vector_results.get('similar_channels', []))
        
        item = {
            'channel_id': {'S': channel_id},
            'review_date': {'S': review_date},
            'channel_name': {'S': channel_name},
            'file_count': {'N': str(content_data['total_files'])},
            'total_content_length': {'N': str(content_data['total_content_length'])},
            'bedrock_response': {'S': analysis_results['raw_response']},
            'bedrock_summary': {'S': summary_results['raw_summary']},
            'similar_channels': {'S': similar_channels_json},
            'vectorization_success': {'BOOL': vector_results.get('vectorization_success', False)},
            'embeddings_generated': {'N': str(vector_results.get('embeddings_generated', 0))},
            'vector_bucket': {'S': vector_results.get('vector_bucket', '')},
            'vector_index': {'S': vector_results.get('index_name', '')},
            'processing_time': {'N': str(processing_time)},
            'status': {'S': 'success'}
        }
        
        dynamodb_client.put_item(
            TableName=table_name,
            Item=item
        )
        
        logger.info(f"Stored results in DynamoDB for channel {channel_id}")
        
    except Exception as e:
        logger.error(f"Error storing results in DynamoDB: {str(e)}", exc_info=True)
        raise

output "technical_agent_id" {
  description = "ID of the technical SOW reviewer agent"
  value       = aws_bedrockagent_agent.technical_agent.agent_id
}

output "technical_agent_alias_id" {
  description = "ID of the technical SOW reviewer agent alias"
  value       = aws_bedrockagent_agent_alias.technical_agent_alias.agent_alias_id
}

output "financial_agent_id" {
  description = "ID of the financial SOW reviewer agent"
  value       = aws_bedrockagent_agent.financial_agent.agent_id
}

output "financial_agent_alias_id" {
  description = "ID of the financial SOW reviewer agent alias"
  value       = aws_bedrockagent_agent_alias.financial_agent_alias.agent_alias_id
}

output "legal_agent_id" {
  description = "ID of the legal SOW reviewer agent"
  value       = aws_bedrockagent_agent.legal_agent.agent_id
}

output "legal_agent_alias_id" {
  description = "ID of the legal SOW reviewer agent alias"
  value       = aws_bedrockagent_agent_alias.legal_agent_alias.agent_alias_id
}

output "bedrock_agent_role_arn" {
  description = "ARN of the Bedrock agent IAM role"
  value       = aws_iam_role.bedrock_agent_role.arn
}

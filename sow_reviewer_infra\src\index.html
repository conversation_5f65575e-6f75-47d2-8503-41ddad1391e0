<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Documents</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f4f6fb;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        .navbar {
            width: 100vw;
            background: #232f3e;
            color: #fff;
            padding: 0.5rem 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            margin-bottom: 2rem;
        }
        .navbar-content {
            max-width: 1100px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        .navbar-title {
            font-weight: bold;
            font-size: 1.3em;
            margin-right: 2rem;
            letter-spacing: 1px;
        }
        .nav-link {
            color: #fff;
            text-decoration: none;
            margin-right: 1.5rem;
            font-size: 1.05em;
            padding: 0.3em 0.7em;
            border-radius: 4px;
            transition: background 0.15s;
        }
        .nav-link:hover, .nav-link.active {
            background: #4f8cff;
            color: #fff;
        }
        .upload-container {
            background: #fff;
            padding: 2rem 3rem;
            border-radius: 12px;
            box-shadow: 0 2px 16px rgba(0,0,0,0.08);
            text-align: center;
            min-width:480px;
            max-width:700px;
            width:100%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        th {
            background: #f4f6fb;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-content">
            <span class="navbar-title">SOW Reviewer</span>
            <a href="index.html" class="nav-link active">All Documents</a>
            <a href="upload.html" class="nav-link">Upload Document</a>
        </div>
    </nav>
    <div class="upload-container">
        <h2>Statements of Work</h2>
        <table id="documents-table">
            <thead>
                <tr>
                    <th>Customer Name</th>
                    <th>Document Name</th>
                    <th>Status</th>
                    <th>Open</th>
                </tr>
            </thead>
            <tbody id="documents-table-body">
                <tr><td colspan="3" style="text-align:center;color:#888;">Loading...</td></tr>
            </tbody>
        </table>
    </div>
    <div class="upload-container" style="margin-top:2rem;">
        <h2>Reviews</h2>
        <table id="reviews-table">
            <thead>
                <tr>
                    <th>Customer Name</th>
                    <th>Review Type</th>
                    <th>Open</th>
                </tr>
            </thead>
            <tbody id="reviews-table-body">
                <tr><td colspan="3" style="text-align:center;color:#888;">Loading...</td></tr>
            </tbody>
        </table>
    </div>
    <script>
    // Fetch and render all review documents in the Reviews table
    async function loadAllReviews() {
        const tableBody = document.getElementById('reviews-table-body');
        tableBody.innerHTML = '<tr><td colspan="3" style="text-align:center;color:#888;">Loading...</td></tr>';
        try {
            // Update this endpoint to your reviews listing API or S3 bucket listing endpoint
            const endpoint = 'https://gdy74fkaja.execute-api.us-east-1.amazonaws.com/dev/reviews';
            const response = await fetch(endpoint, { method: 'GET' });
            const data = await response.json();
            if (response.ok && data.reviews && data.reviews.length > 0) {
                // Sort by customer_name, then review_type
                data.reviews.sort((a, b) => {
                    if (a.customer_name === b.customer_name) {
                        return (a.review_type || '').localeCompare(b.review_type || '');
                    }
                    return (a.customer_name || '').localeCompare(b.customer_name || '');
                });
                tableBody.innerHTML = '';
                data.reviews.forEach(review => {
                    // Use presigned_url if available
                    let reviewUrl = review.presigned_url || '';
                    tableBody.innerHTML +=
                        `<tr>` +
                        `<td>${review.customer_name || ''}</td>` +
                        `<td>${review.review_type || ''}</td>` +
                        `<td>${reviewUrl ? `<a href='${reviewUrl}' target='_blank'>Open document</a>` : ''}</td>` +
                        `</tr>`;
                });
            } else {
                tableBody.innerHTML = '<tr><td colspan="3" style="text-align:center;color:#888;">No reviews found.</td></tr>';
            }
        } catch (err) {
            tableBody.innerHTML = `<tr><td colspan="3" style="color:red;text-align:center;">Error loading reviews: ${err.message}</td></tr>`;
        }
    }

    async function loadAllDocuments() {
        const tableBody = document.getElementById('documents-table-body');
        tableBody.innerHTML = '<tr><td colspan="3" style="text-align:center;color:#888;">Loading...</td></tr>';
        try {
            const endpoint = 'https://gdy74fkaja.execute-api.us-east-1.amazonaws.com/dev/documents';
            const response = await fetch(endpoint, { method: 'GET' });
            const data = await response.json();
            if (response.ok && data.documents && data.documents.length > 0) {
                tableBody.innerHTML = '';
                data.documents.forEach(doc => {
                    const row = document.createElement('tr');
                    // Use presigned_url if available
                    let docUrl = doc.presigned_url || '';
                    row.innerHTML =
                        `<td>${doc.customer_name || ''}</td>` +
                        `<td>${doc.file_name || ''}</td>` +
                        `<td>${doc.status || ''}</td>` +
                        `<td>${docUrl ? `<a href='${docUrl}' target='_blank'>Open document</a>` : ''}</td>`;
                    tableBody.appendChild(row);
                });
            } else {
                tableBody.innerHTML = '<tr><td colspan="3" style="text-align:center;color:#888;">No documents found.</td></tr>';
            }
        } catch (err) {
            tableBody.innerHTML = `<tr><td colspan="3" style="color:red;text-align:center;">Error loading documents: ${err.message}</td></tr>`;
        }
    }
    window.addEventListener('DOMContentLoaded', () => {
        loadAllDocuments();
        loadAllReviews();
    });
    </script>
</body>
</html>

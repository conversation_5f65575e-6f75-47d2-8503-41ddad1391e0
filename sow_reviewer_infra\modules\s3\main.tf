# Single S3 bucket for all SOW operations with organized folder structure
resource "aws_s3_bucket" "sow_main_bucket" {
  bucket = "${var.name_prefix}${var.account_id}-${var.region}-${var.environment}-${var.project_name}-main-bucket"
}

resource "aws_s3_bucket_versioning" "sow_main_bucket_versioning" {
  bucket = aws_s3_bucket.sow_main_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "sow_main_bucket_encryption" {
  bucket = aws_s3_bucket.sow_main_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_cors_configuration" "sow_main_bucket_cors" {
  bucket = aws_s3_bucket.sow_main_bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "GET", "DELETE"]
    allowed_origins = ["*"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

# Create folder structure using S3 objects with zero-byte content
# This ensures the folder structure exists and is visible
resource "aws_s3_object" "folder_structure" {
  for_each = toset([
    "input/.keep",
    "output/.keep", 
    "support_document/.keep"
  ])
  
  bucket  = aws_s3_bucket.sow_main_bucket.bucket
  key     = each.value
  content = ""
  
  tags = var.tags
}

# Dedicated S3 bucket for Slack data
resource "aws_s3_bucket" "slack_data_bucket" {
  bucket = "${var.name_prefix}${var.account_id}-${var.region}-${var.environment}-${var.project_name}-slack-bucket"
}

resource "aws_s3_bucket_versioning" "slack_data_bucket_versioning" {
  bucket = aws_s3_bucket.slack_data_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "slack_data_bucket_encryption" {
  bucket = aws_s3_bucket.slack_data_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
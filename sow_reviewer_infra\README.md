# SOW Reviewer Infrastructure

This repository contains Terraform infrastructure code for the SOW (Statement of Work) Reviewer application. The infrastructure features a modular architecture with clear separation between frontend and backend components, comprehensive logging, and organized S3 structure.

## Architecture Overview

The SOW Reviewer system is organized into distinct frontend and backend components:

### Frontend Components
- **Static Website Hosting**: S3 bucket with CloudFront distribution
- **Authentication**: Cognito User Pool with hosted UI and Lambda@Edge enforcement
- **User Interface**: HTML-based upload interface with secure authentication flow

### Backend Components
- **API Gateway**: REST API with CORS support
- **Lambda Functions**: Two consolidated functions with comprehensive logging
- **Single S3 Bucket**: Organized customer-based folder structure
- **DynamoDB Tables**: Document status tracking and processing results
- **Bedrock Agents**: Three specialized AI agents for different review types

### Architecture Benefits
- **Modular Design**: Clear separation between frontend and backend concerns
- **Simplified Management**: Reduced from 4 to 2 Lambda functions
- **Comprehensive Logging**: Structured logging with detailed error tracking
- **Organized Storage**: Customer-based S3 folder structure (customer/input/output/support_document)
- **Enhanced Error Handling**: Graceful error handling with user-friendly messages
- **Cost Optimization**: Consolidated functions reduce cold start overhead
- **Security**: Cognito authentication with Lambda@Edge enforcement
- **Conflict Prevention**: Configurable naming variables to avoid resource name conflicts

### API Endpoints

The system provides the following REST endpoints:

1. **POST /upload** - Upload SOW documents
2. **POST /process** - Invoke agents to process documents
3. **GET /status/{document_id}** - Check document processing status
4. **GET /documents** - List documents by status (with optional filtering)

### Bedrock Agents

Three specialized agents for different review perspectives:
- **Technical Agent**: Reviews technical feasibility and implementation details
- **Financial Agent**: Reviews financial aspects and funding requirements
- **Legal Agent**: Reviews legal implications and contract language

## Directory Structure

```
.
├── main.tf                 # Main configuration using modules
├── variables.tf            # Input variables and validation
├── outputs.tf              # Output values
├── providers.tf            # Provider configuration
├── data.tf                 # Frontend data sources and resources
├── frontend.tf             # Frontend infrastructure (S3, CloudFront, Cognito)
├── environments/           # Environment-specific configurations
│   ├── dev.tfvars
│   ├── staging.tfvars
│   └── prod.tfvars
├── modules/                # Terraform modules (backend components)
│   ├── api-gateway/        # API Gateway configuration
│   ├── bedrock-agents/     # Bedrock agents setup
│   ├── dynamodb/           # DynamoDB tables
│   ├── lambda/             # Lambda functions
│   └── s3/                 # S3 buckets
├── lambda_edge/            # Lambda@Edge function for authentication
│   ├── raw_main.js.tmpl    # Template for edge function
│   └── function.zip        # Packaged edge function
└── src/                    # Frontend source files
    └── upload.html         # Main upload interface
```

## Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **Terraform** >= 1.0
3. **AWS Account** with:
   - Bedrock access enabled
   - Claude model access approved
   - Appropriate IAM permissions
   - ACM certificate for custom domain (if using custom domain)

## Configuration

### Naming Variables

The infrastructure uses configurable naming variables to avoid resource name conflicts across environments and deployments:

#### Core Naming Variables
- `project_name`: Project name used in resource naming (default: "sow-reviewer")
- `resource_prefix`: Optional prefix for all resource names to avoid conflicts (default: null)
- `environment`: Environment name (dev, staging, prod)

#### Frontend Configuration Variables
- `frontend_domain_name`: Domain name for the frontend (e.g., sowreviewer.protagona.com)
- `cognito_domain_prefix`: Prefix for Cognito user pool domain
- `test_user_username`: Username for the test user created in Cognito
- `test_user_email`: Email for the test user created in Cognito
- `test_user_password`: Temporary password for the test user (sensitive)
- `acm_certificate_arn`: ARN of the ACM certificate for the frontend domain
- `geo_restriction_locations`: List of country codes for CloudFront geo-restriction
- `cloudfront_price_class`: CloudFront price class

#### Resource Naming Pattern
All resources follow a consistent naming pattern:
```
{resource_prefix}-{environment}-{project_name}-{resource_type}
```

Examples:
- `dev-sow-reviewer-main-bucket` (with resource_prefix = "dev")
- `sow-reviewer-api` (without resource_prefix)
- `prod-sow-reviewer-technical-agent` (with resource_prefix = "prod")

### Environment-Specific Configuration

Each environment has its own `.tfvars` file with specific configurations:

#### Development (`environments/dev.tfvars`)
```hcl
environment = "dev"
resource_prefix = "dev"
frontend_domain_name = "sowreviewer.protagona.com"
```

#### Staging (`environments/staging.tfvars`)
```hcl
environment = "staging"
resource_prefix = "staging"
frontend_domain_name = "sowreviewer-staging.protagona.com"
```

#### Production (`environments/prod.tfvars`)
```hcl
environment = "prod"
resource_prefix = "prod"
frontend_domain_name = "sowreviewer.protagona.com"
```

### Avoiding Name Conflicts

To avoid resource name conflicts when deploying multiple environments or instances:

1. **Use resource_prefix**: Set different prefixes for each deployment
   ```hcl
   resource_prefix = "team-a-dev"
   resource_prefix = "team-b-staging"
   ```

2. **Use different project names**: Change the project name for different projects
   ```hcl
   project_name = "sow-reviewer-v2"
   project_name = "contract-analyzer"
   ```

3. **Use different domains**: Configure different frontend domains
   ```hcl
   frontend_domain_name = "sowreviewer-team-a.protagona.com"
   frontend_domain_name = "contractanalyzer.protagona.com"
   ```

## Deployment

### Quick Start with AWS SSO (Recommended)

Use the provided PowerShell deployment script for Windows with AWS SSO:

```powershell
# List available AWS profiles
aws configure list-profiles

# Plan deployment with SSO profile
.\deploy.ps1 -Environment dev -Action plan -Profile your-sso-profile

# Apply deployment with SSO profile (will auto-login if needed)
.\deploy.ps1 -Environment dev -Action apply -Profile your-sso-profile

# Apply with auto-approval (for automation)
.\deploy.ps1 -Environment dev -Action apply -Profile your-sso-profile -AutoApprove

# Destroy infrastructure
.\deploy.ps1 -Environment dev -Action destroy -Profile your-sso-profile
```

**Note**: The script will automatically handle SSO login if your session has expired.

### Manual Deployment

If you prefer manual Terraform commands:

```bash
# 1. Initialize Terraform
terraform init

# 2. Create and select workspace
terraform workspace new dev
# or select existing: terraform workspace select dev

# 3. Plan deployment
terraform plan -var-file="environments/dev.tfvars"

# 4. Apply configuration
terraform apply -var-file="environments/dev.tfvars"
```

### Environment-Specific Deployment

For different environments:

```bash
# Development
terraform workspace select dev
terraform apply -var-file="environments/dev.tfvars"

# Staging
terraform workspace select staging
terraform apply -var-file="environments/staging.tfvars"

# Production
terraform workspace select prod
terraform apply -var-file="environments/prod.tfvars"
```

### Multi-Instance Deployment

To deploy multiple instances of the same environment (e.g., for different teams):

```bash
# Team A Development
terraform workspace select team-a-dev
terraform apply -var-file="environments/dev.tfvars" \
  -var="resource_prefix=team-a" \
  -var="frontend_domain_name=team-a-sowreviewer.protagona.com"

# Team B Development
terraform workspace select team-b-dev
terraform apply -var-file="environments/dev.tfvars" \
  -var="resource_prefix=team-b" \
  -var="frontend_domain_name=team-b-sowreviewer.protagona.com"
```

## Usage

### Frontend Access

After deployment, access the frontend at:
- **Development**: `https://sowreviewer.protagona.com/` (or your configured domain)
- **Authentication**: Use the test user credentials created during deployment

**Test User Credentials** (created automatically):
- Username: `testuser3`
- Password: `TestUser123!`
- **Note**: Password must be changed on first login

### API Usage Examples

#### 1. Upload a Document

```bash
curl -X POST https://your-api-gateway-url/dev/upload \
  -H "Content-Type: application/json" \
  -d '{
    "file_name": "sow_example.txt",
    "file_content": "base64-encoded-file-content",
    "content_type": "text/plain"
  }'
```

Response:
```json
{
  "document_id": "uuid-here",
  "s3_uri": "s3://bucket-name/documents/uuid/filename.txt",
  "message": "File uploaded successfully"
}
```

#### 2. Process Document with Agent

```bash
curl -X POST https://your-api-gateway-url/dev/process \
  -H "Content-Type: application/json" \
  -d '{
    "document_id": "document-uuid",
    "agent_type": "technical"
  }'
```

Agent types: `technical`, `financial`, `legal`

#### 3. Check Document Status

```bash
curl -X GET https://your-api-gateway-url/dev/status/document-uuid
```

#### 4. List Unprocessed Documents

```bash
# List all uploaded documents
curl -X GET https://your-api-gateway-url/dev/documents

# Filter by status
curl -X GET https://your-api-gateway-url/dev/documents?status=uploaded&limit=10
```

## Infrastructure Components

### Frontend Infrastructure (`frontend.tf`)

- **S3 Bucket**: Static website hosting with public read access
- **CloudFront Distribution**: CDN with custom domain and SSL certificate
- **Cognito User Pool**: Authentication with hosted UI
- **Lambda@Edge Function**: Authentication enforcement at the edge
- **IAM Roles**: Frontend-specific permissions

### Backend Infrastructure (Modules)

#### S3 Module (`modules/s3/`)
- **Single main bucket** with organized folder structure
- Customer-based organization: `{customer_name}/input/`, `{customer_name}/output/`, `{customer_name}/support_document/`
- Versioning and AES-256 encryption enabled
- CORS configured for web uploads

#### DynamoDB Module (`modules/dynamodb/`)
- Document status tracking table with GSI for status queries
- Processing results table for storing agent outputs
- Pay-per-request billing for cost optimization

#### Bedrock Agents Module (`modules/bedrock-agents/`)
- Three specialized agents with environment-specific naming
- IAM roles and policies for agent execution
- Agent aliases for version management

#### Lambda Module (`modules/lambda/`)
- **Two consolidated Lambda functions**:
  - **Agent Operations**: Handles all agent-related processing with comprehensive logging
  - **General Operations**: Handles upload, status, and listing operations
- Shared IAM role with comprehensive permissions for S3, DynamoDB, and Bedrock access
- Environment variables for resource references
- Enhanced error handling and structured logging

#### API Gateway Module (`modules/api-gateway/`)
- REST API with CORS support
- Four main endpoints with proper routing
- Lambda integrations and permissions

### Data Sources (`data.tf`)

Contains only frontend-related data sources and resources:
- AWS account and region information
- Lambda@Edge function template and packaging
- Frontend-specific configurations

## Security

- All S3 buckets use AES256 encryption
- Lambda functions run with least-privilege IAM roles
- API Gateway endpoints are public but can be secured with API keys
- DynamoDB tables use AWS managed encryption
- Bedrock agents have scoped permissions
- Cognito authentication with secure session management
- Lambda@Edge enforces authentication at the CDN level

## Monitoring and Logging

- All Lambda functions include CloudWatch logging
- API Gateway has access logging available
- Resource tagging for cost allocation and monitoring
- Environment-specific naming for resource identification
- Frontend access logs via CloudFront

## Costs

The infrastructure uses serverless and pay-per-use services:
- Lambda: Pay per invocation and duration
- DynamoDB: Pay-per-request billing
- S3: Pay for storage and requests
- API Gateway: Pay per API call
- Bedrock: Pay per model invocation
- CloudFront: Pay for data transfer and requests
- Cognito: Pay per MAU (Monthly Active Users)

## Testing

### Testing Suite

A comprehensive testing suite is available in the `testing/` directory to validate the deployed infrastructure:

#### **Quick Start**
```bash
# Navigate to testing directory
cd testing

# Set API URL (replace with your actual URL)
export SOW_API_URL="https://your-api-gateway-url.execute-api.us-east-1.amazonaws.com/dev"

# Run complete test suite
./test_all.sh
```

#### **Individual Tests**

**File-Based Upload (Recommended)**
```bash
# Upload a real SOW document
./test_upload.sh sample_sow.txt

# Upload with specific customer name
./test_upload.sh sample_sow.txt "TechCorp Solutions Inc."

# Upload any SOW file from your system
./test_upload.sh /path/to/your/sow_document.txt "Your Customer Name"
```

**Legacy Tests (using hardcoded test data)**
```bash
# Health check
./test_health.sh

# Upload test document (legacy)
./test_upload.sh

# Process with AI agent
./test_process.sh <document_id> technical

# Check status
./test_status.sh <document_id>

# List documents
./test_list.sh uploaded
```

#### **Complete Workflow Example**
```bash
# 1. Upload a real SOW document
./test_upload.sh sample_sow.txt "TechCorp Solutions Inc."

# 2. Process with all three agents
./test_process.sh $(cat .last_document_id) technical
./test_process.sh $(cat .last_document_id) financial
./test_process.sh $(cat .last_document_id) legal

# 3. Check final status
./test_status.sh $(cat .last_document_id)

# 4. List all documents
./test_list.sh uploaded
```

#### **Windows PowerShell**
```powershell
# Health check
.\test_health.ps1
```

#### **Sample Documents**
The testing directory includes sample documents for testing:
- `sample_sow.txt` - Comprehensive SOW with technical, financial, and legal sections
- Create your own SOW documents following the same format

#### **Test Coverage**
The testing suite validates:
- ✅ API Gateway connectivity and CORS
- ✅ Document upload and storage (file-based and legacy)
- ✅ AI agent processing (technical, financial, legal)
- ✅ Status tracking and monitoring
- ✅ End-to-end workflow completion
- ✅ Customer name extraction from documents
- ✅ Multi-agent processing workflow

For detailed testing documentation, see [testing/README.md](testing/README.md).

## Troubleshooting

### Common Issues

1. **Bedrock Access**: Ensure Bedrock service is enabled in your region
2. **Model Access**: Verify Claude model access is approved
3. **IAM Permissions**: Check that deployment role has sufficient permissions
4. **Region Availability**: Ensure all services are available in selected region
5. **Cognito Domain**: Verify Cognito domain is properly configured
6. **CloudFront Distribution**: Check that the distribution is deployed and accessible
7. **Resource Name Conflicts**: Use `resource_prefix` to avoid naming conflicts

### Logs

Check CloudWatch logs for the Lambda functions:
- **Agent Operations**: `/aws/lambda/{env}-sow-agent-operations` - All agent processing, customer name extraction, and reviews
- **General Operations**: `/aws/lambda/{env}-sow-general-operations` - Upload, status, and document listing operations
- **Lambda@Edge**: `/aws/lambda/us-east-1.{function_name}` - Frontend authentication enforcement

**Log Features**:
- Structured logging with request IDs for tracing
- Performance metrics (processing times, file sizes)
- Detailed error tracking with stack traces
- Operation success/failure indicators

### Frontend Issues

1. **Authentication Problems**: Check Cognito User Pool configuration
2. **Domain Issues**: Verify CloudFront distribution and custom domain setup
3. **Lambda@Edge Errors**: Check edge function logs in us-east-1 region

### Name Conflict Resolution

If you encounter resource name conflicts:

1. **Check existing resources**: Use AWS CLI to list existing resources
   ```bash
   aws s3 ls | grep sow
   aws lambda list-functions --query 'Functions[?contains(FunctionName, `sow`)]'
   ```

2. **Use different prefixes**: Modify your tfvars file
   ```hcl
   resource_prefix = "team-a-dev"
   ```

3. **Use different project names**: Change the project name
   ```hcl
   project_name = "sow-reviewer-v2"
   ```

4. **Use different domains**: Configure unique domains
   ```hcl
   frontend_domain_name = "team-a-sowreviewer.protagona.com"
   ```

## Contributing

1. Make changes in feature branches
2. Test in development environment first
3. Update documentation as needed
4. Submit pull requests for review

## License

[Add your license information here]

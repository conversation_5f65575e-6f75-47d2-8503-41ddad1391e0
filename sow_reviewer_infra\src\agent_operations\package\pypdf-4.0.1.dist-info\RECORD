pypdf-4.0.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pypdf-4.0.1.dist-info/LICENSE,sha256=qXrCMOXzPvEKU2eoUOsB-R8aCwZONHQsd5TSKUVX9SQ,1605
pypdf-4.0.1.dist-info/METADATA,sha256=m61MDtpGZ3nT4-Z0XL9DOkRsFah_5hxSF1fSyXMdUMQ,7407
pypdf-4.0.1.dist-info/RECORD,,
pypdf-4.0.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf-4.0.1.dist-info/WHEEL,sha256=EZbGkh7Ie4PoZfRQ8I0ZuP9VklN_TvcZ6DSE5Uar4z4,81
pypdf/__init__.py,sha256=XzM1U8gTAWm3vv81KCF4X7xSQmCjqGiPKHjNPJvW9nc,1292
pypdf/__pycache__/__init__.cpython-313.pyc,,
pypdf/__pycache__/_cmap.cpython-313.pyc,,
pypdf/__pycache__/_encryption.cpython-313.pyc,,
pypdf/__pycache__/_merger.cpython-313.pyc,,
pypdf/__pycache__/_page.cpython-313.pyc,,
pypdf/__pycache__/_page_labels.cpython-313.pyc,,
pypdf/__pycache__/_protocols.cpython-313.pyc,,
pypdf/__pycache__/_reader.cpython-313.pyc,,
pypdf/__pycache__/_utils.cpython-313.pyc,,
pypdf/__pycache__/_version.cpython-313.pyc,,
pypdf/__pycache__/_writer.cpython-313.pyc,,
pypdf/__pycache__/_xobj_image_helpers.cpython-313.pyc,,
pypdf/__pycache__/constants.cpython-313.pyc,,
pypdf/__pycache__/errors.cpython-313.pyc,,
pypdf/__pycache__/filters.cpython-313.pyc,,
pypdf/__pycache__/pagerange.cpython-313.pyc,,
pypdf/__pycache__/papersizes.cpython-313.pyc,,
pypdf/__pycache__/types.cpython-313.pyc,,
pypdf/__pycache__/xmp.cpython-313.pyc,,
pypdf/_cmap.py,sha256=HtX3Ha5swpi3LqgIDXp2eggvL8Hpu8zc6YK5rNX2MBc,17997
pypdf/_codecs/__init__.py,sha256=15Fls0Fzl2NXKJyGNO4ozWveYCbOtDkdFiUSUpxHVGQ,1674
pypdf/_codecs/__pycache__/__init__.cpython-313.pyc,,
pypdf/_codecs/__pycache__/adobe_glyphs.cpython-313.pyc,,
pypdf/_codecs/__pycache__/pdfdoc.cpython-313.pyc,,
pypdf/_codecs/__pycache__/std.cpython-313.pyc,,
pypdf/_codecs/__pycache__/symbol.cpython-313.pyc,,
pypdf/_codecs/__pycache__/zapfding.cpython-313.pyc,,
pypdf/_codecs/adobe_glyphs.py,sha256=jrMZTzGFE8aMEuwfNJ4JZh_GZypPBg6SLE1oaC9DRTU,447237
pypdf/_codecs/pdfdoc.py,sha256=xfSvMFYsvxuaSQ0Uu9vZDKaB0Wu85h1uCiB1i9rAcUU,4269
pypdf/_codecs/std.py,sha256=DyQMuEpAGEpS9uy1jWf4cnj-kqShPOAij5sI7Q1YD8E,2630
pypdf/_codecs/symbol.py,sha256=nIaGQIlhWCJiPMHrwUlmGHH-_fOXyEKvguRmuKXcGAk,3734
pypdf/_codecs/zapfding.py,sha256=PQxjxRC616d41xF3exVxP1W8nM4QrZfjO3lmtLxpE_s,3742
pypdf/_crypt_providers/__init__.py,sha256=O6cOQ1QYca10IV_YDo1RE6PzCs-rxL9pNGmP__nRGkE,3054
pypdf/_crypt_providers/__pycache__/__init__.cpython-313.pyc,,
pypdf/_crypt_providers/__pycache__/_base.cpython-313.pyc,,
pypdf/_crypt_providers/__pycache__/_cryptography.cpython-313.pyc,,
pypdf/_crypt_providers/__pycache__/_fallback.cpython-313.pyc,,
pypdf/_crypt_providers/__pycache__/_pycryptodome.cpython-313.pyc,,
pypdf/_crypt_providers/_base.py,sha256=_f53Mj6vivhEZMQ4vNxN5G0IOgFY-n5_leke0c_qiNU,1711
pypdf/_crypt_providers/_cryptography.py,sha256=HTw4Ti43ed8kFSysly_YTH-lDRQlBI4PU9DPNLvPgu8,4329
pypdf/_crypt_providers/_fallback.py,sha256=PVDQQrq389VbaBqOHxXfoyCk9bLYgFrrDKVpNXzTdx8,3345
pypdf/_crypt_providers/_pycryptodome.py,sha256=U1aQZ9iYBrZo-hKCjJUhGOPhwEFToiitowQ316TNrrA,3381
pypdf/_encryption.py,sha256=12zEvbWoZeHZlf6ZECtp3qTKrZejBTNmy-9u82avBjs,48920
pypdf/_merger.py,sha256=kLZ04OEXLuAb2ij8C8xZcKauVWEawc22b2d4aAOfGWQ,24442
pypdf/_page.py,sha256=YyJJM6KSuKwkT4CgeNrzoDDoQ60QYgLvWgOJhBhnNmY,93259
pypdf/_page_labels.py,sha256=XzUWAwjCl72TQqn1TI_OnhoH42OVT4qTVnIYJt2nIek,7186
pypdf/_protocols.py,sha256=oDUwVBhfokG1Rh1_Wr3X7jSV9DS5ZGPVPf_Jgs3bOaU,1897
pypdf/_reader.py,sha256=-uMbyywVtBDZhXqR3wEWJ3zsruJeU8rkpey_gz4F2Kc,78963
pypdf/_text_extraction/__init__.py,sha256=YmSRITJ4VqdWlbTY9dJ57OWTfB1bUmdcQJCEydZB8Bc,10373
pypdf/_text_extraction/__pycache__/__init__.cpython-313.pyc,,
pypdf/_text_extraction/_layout_mode/__init__.py,sha256=BPWqqo0ggSKQf-2owsaN_vVvyqsnaZlpIWY3N1bzegc,338
pypdf/_text_extraction/_layout_mode/__pycache__/__init__.cpython-313.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_fixed_width_page.cpython-313.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_font.cpython-313.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_font_widths.cpython-313.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_text_state_manager.cpython-313.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_text_state_params.cpython-313.pyc,,
pypdf/_text_extraction/_layout_mode/_fixed_width_page.py,sha256=K0Cr5CgdjGR27J4ErAah2xeeKrq17VaPZQ9L4pbqmmc,14782
pypdf/_text_extraction/_layout_mode/_font.py,sha256=CMVOtHc9Al10zqsr3L13qmX2anKp8ynILyYKPuVnOq0,5099
pypdf/_text_extraction/_layout_mode/_font_widths.py,sha256=f4Q1acWC_iiLhzKjyfoF8_FFz7wlRlLtXE1Vs6Ifsbo,4264
pypdf/_text_extraction/_layout_mode/_text_state_manager.py,sha256=uPda8l5IPtK3E7XgeP8eYtSP6CZ8KMGSky37iBX1CDQ,8012
pypdf/_text_extraction/_layout_mode/_text_state_params.py,sha256=oKVgKfU_dcoDJQr8k_yqQaoXRqFjyad7IC9JoW4S0Fc,5316
pypdf/_utils.py,sha256=BcSUV1istw_8YBCVDecLh5owh7oSyH0bhILsxRZgSLU,20153
pypdf/_version.py,sha256=v4mcX8QEz7plObRyvTdDblE04TUjya-nJkDSAikxv9Y,22
pypdf/_writer.py,sha256=7sQ2iWJmHxIHcPRKu2aPTos86ARjMQPq4xrOBW9xV3U,114365
pypdf/_xobj_image_helpers.py,sha256=DIFCp-PLe9mKUYhSDLRj9eik9QOn0e-wg0UIi6VzR9E,10925
pypdf/annotations/__init__.py,sha256=5Ojv2DCGTfXwhxbSBcrcaU9Y2Q1O5QmBRMcWgyxPcxQ,1118
pypdf/annotations/__pycache__/__init__.cpython-313.pyc,,
pypdf/annotations/__pycache__/_base.cpython-313.pyc,,
pypdf/annotations/__pycache__/_markup_annotations.cpython-313.pyc,,
pypdf/annotations/__pycache__/_non_markup_annotations.cpython-313.pyc,,
pypdf/annotations/_base.py,sha256=axdUsbNrhD1bHCJoWSKCspgNGe61aK2Vzn9jlaEq3zc,910
pypdf/annotations/_markup_annotations.py,sha256=48K7_4gcNdkd3MV5dKR3Ygx5BQsyHgzbJm01wn0mJWM,11597
pypdf/annotations/_non_markup_annotations.py,sha256=a8wCa_0-ZAhOWfnLMrkmVnGBTBy6QkfLyeUIz5G0TQ0,1278
pypdf/constants.py,sha256=zYn0IaNGVybiBrMh-Zjgqw-eng2ZgByecuFggkX_i8g,19217
pypdf/errors.py,sha256=G2_fqPaJneNR4Q5o8ELem77qW00xdx9dtPc5RWwGGJI,1632
pypdf/filters.py,sha256=pVT7Oh5KvKuq-VUlAKVDzoU0n7Zx0nfNgJ0KfNvTZwk,30533
pypdf/generic/__init__.py,sha256=uTumTEJzmlL5R84hiKGJ5IglidfnbdEXy9MVL-mjcFc,15097
pypdf/generic/__pycache__/__init__.cpython-313.pyc,,
pypdf/generic/__pycache__/_base.cpython-313.pyc,,
pypdf/generic/__pycache__/_data_structures.cpython-313.pyc,,
pypdf/generic/__pycache__/_fit.cpython-313.pyc,,
pypdf/generic/__pycache__/_outline.cpython-313.pyc,,
pypdf/generic/__pycache__/_rectangle.cpython-313.pyc,,
pypdf/generic/__pycache__/_utils.cpython-313.pyc,,
pypdf/generic/__pycache__/_viewerpref.cpython-313.pyc,,
pypdf/generic/_base.py,sha256=JVh7wlHYnQ5Lunu__67WEEYohPjvrwNJ9rhUSSO8ROQ,23199
pypdf/generic/_data_structures.py,sha256=M5FZ0oRY95eagyCh76ue3I529ZfguLwFlAGLNXyhInY,55915
pypdf/generic/_fit.py,sha256=_2bbohJwlpEltNaLp8j5t8dSNs3270av_d8A5FvM32o,5427
pypdf/generic/_outline.py,sha256=b8NsPZeDaO_s1ZRILwKFPEaofAhhyfDGOS06cm4YxRk,1090
pypdf/generic/_rectangle.py,sha256=7ZcTWfA3pcjeLcV5yJAgfJ0oNQ-bq1zuaqiuIPd1moo,3808
pypdf/generic/_utils.py,sha256=KogUXJL3bdR9x8CWUQVvAJa1nj1nvmJdB1_pUbXdKyc,6420
pypdf/generic/_viewerpref.py,sha256=urolnUJqs0Arb6YSbFcjOqU0Y55EuwJ18_QuWoZ1GPA,6327
pypdf/pagerange.py,sha256=6S6QOKzvA2lcFrzksSh8J4hs4uwgIl_53s4ilPcPq5o,6880
pypdf/papersizes.py,sha256=ACzkcH77rrMfn5lp66mZPoYaFJiPjdwEetIQlOBXGMw,1367
pypdf/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf/types.py,sha256=4l46jq0Q1h0MNDz0-G_d6ZSO2OiaN-GZgEyVrqK19cY,2085
pypdf/xmp.py,sha256=YlnXw0I__-YzIE85DKobAGGjnthDB5zrMWk8gafKUW4,14192

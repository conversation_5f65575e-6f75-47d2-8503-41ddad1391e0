# Environment Configuration for Staging
environment = "staging"
region      = "us-east-1"

# Naming configuration to avoid conflicts
project_name = "sow-reviewer"
resource_prefix = "staging"  # Optional: Add prefix to avoid conflicts in shared accounts

# Frontend configuration
frontend_domain_name = "sowreviewer-staging.protagona.com"
cognito_domain_prefix = "sowreviewer-staging-frontend-domain"
test_user_username = "testuser3"
test_user_email = "<EMAIL>"
test_user_password = "TestUser123!"

# CloudFront configuration
cloudfront_price_class = "PriceClass_200"
geo_restriction_locations = ["US", "CA", "GB", "DE", "EG"]

# Foundation model ARN - using Claude Sonnet 4 inference profile
foundation_model_arn = "arn:aws:bedrock:us-east-1:************:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"

# Slack configuration (uncomment and configure when ready for staging)
# slack_bot_token    = "xoxb-your-staging-bot-token"
# slack_app_token    = ""  # Not required by lambda function
# slack_workspace_id = "T022F24MK97"  # Protagona Slack workspace ID

# S3 Vectors configuration for channel similarity search
s3_vectors_bucket_name = null  # Will use auto-generated name: staging-************-us-east-1-staging-slack-vectors
s3_vectors_index_name  = "channel-summaries"

# Default tags applied to all resources
default_tags = {
  Environment   = "staging"
  Project       = "SOW Reviewer"
  Owner         = "Development Team"
  CostCenter    = "Staging"
  Backup        = "true"
  AutoShutdown  = "false"
  CreatedBy     = "Terraform"
}

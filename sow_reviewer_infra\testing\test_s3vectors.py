import argparse
import json
import os
import sys
from datetime import datetime
from typing import List, Dict, Any

import boto3


def get_boto3_clients():
    region = os.getenv('AWS_REGION') or os.getenv('AWS_DEFAULT_REGION') or 'us-east-1'
    bedrock = boto3.client('bedrock-runtime', region_name=region)
    s3vectors = boto3.client('s3vectors', region_name=region)
    return bedrock, s3vectors


def generate_embeddings(bedrock, text: str, dimensions: int = 1024) -> List[float]:
    body = {
        'inputText': text,
        'dimensions': dimensions,
        'normalize': True,
    }
    resp = bedrock.invoke_model(
        modelId='amazon.titan-embed-text-v2:0',
        body=json.dumps(body),
    )
    data = json.loads(resp['body'].read())
    embedding = data.get('embedding') or data.get('embeddings')
    if not embedding:
        raise RuntimeError(f"No embedding returned: {data}")
    # Ensure float conversion
    return [float(x) for x in embedding]


def upsert_vector(args):
    bedrock, s3vectors = get_boto3_clients()

    # Prepare data
    key = args.key
    text = args.text
    index_name = args.index
    bucket_name = args.bucket

    print(f"Embedding text for upsert (len={len(text)} chars) ...")
    embeddings = generate_embeddings(bedrock, text)

    # Optional minimal metadata
    metadata: Dict[str, Any] = {
        'source': args.source or 'local_test',
        'title': args.title or 'Untitled',
        'upserted_at': datetime.utcnow().isoformat() + 'Z',
        'text_preview': text[:200],
    }

    print("Putting vector to S3 Vectors ...")
    s3vectors.put_vectors(
        vectorBucketName=bucket_name,
        indexName=index_name,
        vectors=[{
            'key': key,
            'data': {
                'float32': embeddings
            },
            'metadata': metadata,
        }]
    )
    print("Upsert complete.")


def query_vectors(args):
    bedrock, s3vectors = get_boto3_clients()

    question = args.question
    index_name = args.index
    bucket_name = args.bucket
    top_k = int(args.top_k)

    print(f"Embedding question (len={len(question)} chars) ...")
    q_emb = generate_embeddings(bedrock, question)

    print("Querying S3 Vectors ...")
    resp = s3vectors.query_vectors(
        vectorBucketName=bucket_name,
        indexName=index_name,
        queryVector={'float32': q_emb},
        topK=top_k,
        returnMetadata=True,
        returnDistance=True,
    )
    if getattr(args, 'debug', False):
        try:
            print("Raw response:")
            print(json.dumps(resp, indent=2, default=str))
        except Exception:
            pass
    results = resp.get('results') or resp.get('vectors') or []
    print(f"Found {len(results)} results:\n")
    for i, r in enumerate(results, start=1):
        key = r.get('key') or r.get('Key')
        md = r.get('metadata') or {}
        distance = r.get('distance')
        sim = None
        if distance is not None:
            try:
                sim = 1.0 - float(distance)
            except Exception:
                sim = None
        title = md.get('title') or md.get('project_title') or 'N/A'
        preview = (md.get('text_preview') or md.get('technology_stack') or '')
        if isinstance(preview, str) and len(preview) > 120:
            preview = preview[:120] + '...'
        print(f"{i}. key={key} distance={distance} similarity={sim} title={title}")
        if preview:
            print(f"   preview: {preview}")


def query_zero(args):
    _, s3vectors = get_boto3_clients()

    index_name = args.index
    bucket_name = args.bucket
    top_k = int(args.top_k)
    dim = int(args.dim)

    zero_vec = [0.0] * dim
    print(f"Querying with zero vector (dim={dim}) ...")
    resp = s3vectors.query_vectors(
        vectorBucketName=bucket_name,
        indexName=index_name,
        queryVector={'float32': zero_vec},
        topK=top_k,
        returnMetadata=True,
        returnDistance=True,
    )
    try:
        if getattr(args, 'debug', False):
            print(json.dumps(resp, indent=2, default=str))
    except Exception:
        pass
    results = resp.get('results') or resp.get('vectors') or []
    print(f"Found {len(results)} results:\n")
    for i, r in enumerate(results, start=1):
        key = r.get('key') or r.get('Key')
        md = r.get('metadata') or {}
        title = md.get('title') or md.get('project_title') or 'N/A'
        distance = r.get('distance')
        print(f"{i}. key={key} title={title} distance={distance}")


def list_vectors(args):
    bedrock, s3vectors = get_boto3_clients()

    index_name = args.index
    bucket_name = args.bucket
    top_k = int(args.top_k)
    dim = int(args.dim)

    print("Listing vectors ...")
    # Try a native list API if present
    try:
        if hasattr(s3vectors, 'list_vectors'):
            resp = s3vectors.list_vectors(
                vectorBucketName=bucket_name,
                indexName=index_name,
                maxResults=top_k,
            )
            items = resp.get('vectors', [])
            print(f"Found {len(items)} vectors:\n")
            for i, v in enumerate(items, start=1):
                key = v.get('key') or v.get('Key')
                md = v.get('metadata') or {}
                title = md.get('title') or md.get('project_title') or 'N/A'
                print(f"{i}. key={key} title={title}")
            return
    except Exception as e:
        print(f"list_vectors not available or failed: {e}. Falling back to query.")

    # Fallback: query with a zero vector to retrieve top_k items
    zero_vec = [0.0] * dim
    resp = s3vectors.query_vectors(
        vectorBucketName=bucket_name,
        indexName=index_name,
        queryVector={'float32': zero_vec},
        topK=top_k,
        returnMetadata=True,
        returnDistance=True,
    )
    results = resp.get('results', [])
    print(f"Found {len(results)} vectors (via query fallback):\n")
    for i, r in enumerate(results, start=1):
        key = r.get('key') or r.get('Key')
        md = r.get('metadata') or {}
        title = md.get('title') or md.get('project_title') or 'N/A'
        print(f"{i}. key={key} title={title}")


def main():
    parser = argparse.ArgumentParser(description='Local tester for S3 Vectors with Bedrock embeddings')
    sub = parser.add_subparsers(dest='cmd', required=True)

    # Upsert command
    p_up = sub.add_parser('upsert', help='Upsert a text as a vector')
    p_up.add_argument('--bucket', required=True, help='S3 Vectors bucket name')
    p_up.add_argument('--index', required=True, help='S3 Vectors index name')
    p_up.add_argument('--key', required=True, help='Unique key/id for the vector')
    p_up.add_argument('--text', required=True, help='Text to embed and store')
    p_up.add_argument('--source', help='Metadata: source label')
    p_up.add_argument('--title', help='Metadata: title')
    p_up.set_defaults(func=upsert_vector)

    # Query command
    p_q = sub.add_parser('query', help='Query similar vectors for a question')
    p_q.add_argument('--bucket', required=True, help='S3 Vectors bucket name')
    p_q.add_argument('--index', required=True, help='S3 Vectors index name')
    p_q.add_argument('--question', required=True, help='Question text to embed and search')
    p_q.add_argument('--top-k', default='5', help='Number of results to return')
    p_q.add_argument('--debug', action='store_true', help='Print raw response JSON')
    p_q.set_defaults(func=query_vectors)

    # Query zero-vector
    p_qz = sub.add_parser('query-zero', help='Query with a zero vector (debug/validation)')
    p_qz.add_argument('--bucket', required=True, help='S3 Vectors bucket name')
    p_qz.add_argument('--index', required=True, help='S3 Vectors index name')
    p_qz.add_argument('--top-k', default='5', help='Number of results to return')
    p_qz.add_argument('--dim', default='1024', help='Vector dimension')
    p_qz.add_argument('--debug', action='store_true', help='Print raw response JSON')
    p_qz.set_defaults(func=query_zero)

    # List command
    p_l = sub.add_parser('list', help='List vectors in the index')
    p_l.add_argument('--bucket', required=True, help='S3 Vectors bucket name')
    p_l.add_argument('--index', required=True, help='S3 Vectors index name')
    p_l.add_argument('--top-k', default='50', help='Max number of items to list')
    p_l.add_argument('--dim', default='1024', help='Vector dimension (for query fallback)')
    p_l.set_defaults(func=list_vectors)

    args = parser.parse_args()

    try:
        args.func(args)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()



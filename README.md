# SOW Reviewer — Repository Documentation

This repository contains the infrastructure and supporting code for the SOW (Statement of Work) Reviewer system. It includes Terraform modules to provision AWS resources, Lambda functions for backend logic, a simple frontend for uploads, and utilities like a Slack channel reviewer.

## High-Level Architecture

```mermaid
flowchart TB
  subgraph Frontend
    CF[CloudFront CDN]
    S3Web[S3 Static Website]
    Cognito[Cognito User Pool]
  end

  subgraph API
    APIGW[API Gateway]
    LambdaGeneral[Lambda: General Operations]\n- upload\n- list\n- status
    LambdaAgent[Lambda: Agent Operations]\n- process via Bedrock
  end

  subgraph Data
    S3Main[S3 Main Bucket]\ncustomer/ input|output|support
    DDBStatus[DynamoDB: Document Status]
    DDBResults[DynamoDB: Processing Results]
  end

  subgraph AI
    Bedrock[Amazon Bedrock]\nClaude models
    Agents[Bedrock Agents]\ntechnical | financial | legal
  end

  subgraph SlackReview(Optional)
    SlackScraper[Slack Scraper]\n(ingests channel logs)
    SlackBucket[S3: Slack Data]
    SlackReviewLambda[Lambda: Slack Channel Reviewer]\n(bedrock-runtime, s3vectors, DynamoDB)
  end

  User((User))

  User -->|HTTPS| CF
  CF --> S3Web
  CF --> Cognito
  User -->|Authenticated calls| APIGW
  APIGW --> LambdaGeneral
  APIGW --> LambdaAgent

  LambdaGeneral --> S3Main
  LambdaGeneral --> DDBStatus
  LambdaGeneral --> DDBResults

  LambdaAgent --> S3Main
  LambdaAgent --> DDBStatus
  LambdaAgent --> DDBResults
  LambdaAgent --> Agents
  Agents --> Bedrock

  SlackScraper --> SlackBucket
  SlackReviewLambda --> SlackBucket
  SlackReviewLambda --> DDBResults
  SlackReviewLambda --> Bedrock
```

## Key Components

- **Frontend**: Static site on S3 behind CloudFront, authentication via Cognito, optional Lambda@Edge for auth enforcement.
- **API**: API Gateway routing to two primary Lambdas: General Operations and Agent Operations.
- **Data Layer**: Single S3 bucket with organized customer folders; DynamoDB tables for status and results.
- **AI Processing**: Amazon Bedrock Agents invoking Claude models for technical, financial, and legal reviews.
- **Slack Channel Reviewer**: Optional Lambda that reads Slack-exported text files from S3 and produces structured insights via Bedrock.

## Where to Start

- For full infrastructure details, deployment steps, and configuration variables, see:
  - `sow_reviewer_infra/README.md`
- Slack review Lambda entry point:
  - `sow_reviewer_infra/src/slack_channel_reviewer/lambda_function.py`
- Test scripts and examples:
  - `sow_reviewer_infra/testing/`

## Repository Structure

```
.
├── README.md                        # This file (high-level overview + diagram)
└── sow_reviewer_infra/              # Terraform IaC and app code
    ├── README.md                    # Detailed infra docs and usage
    ├── environments/                # dev/staging/prod tfvars
    ├── modules/                     # api-gateway, lambda, s3, dynamodb, bedrock-agents
    ├── src/                         # frontend, lambdas, slack tools
    ├── testing/                     # scripts and utilities for verification
    └── lambda_edge/                 # Lambda@Edge auth template and build
```

## Notes

- Ensure you have access to Amazon Bedrock and the required models before deployment.
- Configure environment-specific variables via `environments/*.tfvars`.
- For name collision avoidance across environments, use the `resource_prefix` variable as documented in the infra README.
import json
import boto3
import uuid
import base64
from datetime import datetime, timezone
from boto3.dynamodb.conditions import Key
import os
import logging
import mimetypes

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

ALLOWED_CONTENT_TYPES = {
    'text/plain',
    'application/pdf'
}


def lambda_handler(event, context):
    """
    Main handler for general operations
    Routes to appropriate function based on the operation
    """
    try:
        logger.info(f"General operations event received: {json.dumps(event)}")
        logger.info(f"Context: {context}")
        
        # Extract operation from path or body
        path = event.get('path', '')
        http_method = event.get('httpMethod', '')
        
        logger.info(f"Path: {path}, Method: {http_method}")
        
        # Route based on path and method
        if '/upload' in path and http_method == 'POST':
            return upload_document(event, context)
        elif '/status' in path and http_method == 'GET':
            return get_status(event, context)
        elif '/documents' in path and http_method == 'GET':
            return list_documents(event, context)
        elif '/reviews' in path and http_method == 'GET':
            return fetch_reviews(event, context)
        elif http_method == 'OPTIONS':
            # Handle CORS preflight requests
            return {
                'statusCode': 200,
                'headers': get_cors_headers(),
                'body': ''
            }
        else:
            logger.error(f"Unsupported operation: {path} {http_method}")
            return {
                'statusCode': 400,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'Unsupported operation'})
            }
    except Exception as e:
        logger.error(f"Unexpected error in main handler: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'headers': get_cors_headers(),
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }

def fetch_reviews(event, context):
    """Fetch reviews from DynamoDB and return as JSON"""
    try:
        logger.info("Fetching reviews from DynamoDB")
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(os.environ['PROCESSING_RESULTS_TABLE'])

        # Optionally filter by customer_name or other params
        query_params = event.get('queryStringParameters') or {}
        customer_name = query_params.get('customer_name')
        limit = int(query_params.get('limit', 50))

        # Scan for all reviews (optionally filter by customer_name)
        scan_kwargs = {'Limit': limit}
        response = table.scan(**scan_kwargs)

        reviews = []
        for item in response.get('Items', []):
            if customer_name and item.get('customer_name', '').lower() != customer_name.lower():
                continue
            review = {
                'document_id': item.get('document_id'),
                'customer_name': item.get('customer_name'),
                'review_type': item.get('agent_type'),
                'created_at': item.get('created_at'),
                's3_uri': item.get('s3_uri'),
                'presigned_url': None
            }
            # Generate presigned_url if s3_uri is present
            s3_uri = item.get('s3_uri', '')
            if s3_uri.startswith('s3://'):
                try:
                    _, _, bucket_and_key = s3_uri.partition('s3://')
                    bucket, _, key = bucket_and_key.partition('/')
                    if bucket and key:
                        s3_client = boto3.client('s3')
                        review['presigned_url'] = s3_client.generate_presigned_url(
                            'get_object',
                            Params={'Bucket': bucket, 'Key': key},
                            ExpiresIn=3600
                        )
                except Exception as e:
                    logger.error(f"Error generating presigned URL for {s3_uri}: {str(e)}")
            reviews.append(review)

        logger.info(f"Fetched {len(reviews)} reviews")
        return {
            'statusCode': 200,
            'headers': get_cors_headers(),
            'body': json.dumps({'reviews': reviews, 'count': len(reviews)})
        }
    except Exception as e:
        logger.error(f"Error fetching reviews: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'headers': get_cors_headers(),
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }


def upload_document(event, context):
    """Upload document to S3 and track in DynamoDB"""
    try:
        logger.info("Starting document upload")
        
        # Extract file data from event
        body = json.loads(event['body']) if isinstance(event.get('body'), str) else event.get('body', {})
        
        file_content = body.get('file_content')  # Base64 encoded file content
        file_name = body.get('file_name')
        content_type = body.get('content_type')
        customer_name = body.get('customer_name', 'Unknown_Customer')
        
        logger.info(f"Upload request - file: {file_name}, type: {content_type}, customer: {customer_name}")
        
        if not file_content or not file_name:
            logger.error("Missing required upload parameters")
            return {
                'statusCode': 400,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'file_content and file_name are required'})
            }
        
        # Sanitize and normalize filename
        safe_file_name = os.path.basename(file_name)
        
        # Infer content type from filename if not provided
        if not content_type:
            guessed, _ = mimetypes.guess_type(safe_file_name)
            content_type = guessed or 'application/octet-stream'
        
        # Validate content type (allow txt, pdf only)
        if content_type not in ALLOWED_CONTENT_TYPES:
            logger.error(f"Unsupported content type: {content_type}")
            return {
                'statusCode': 400,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'Unsupported content_type. Supported: text/plain, application/pdf'})
            }
        
        # Generate unique document ID
        document_id = str(uuid.uuid4())
        logger.info(f"Generated document ID: {document_id}")
        
        # Sanitize customer name for S3 key
        customer_name_clean = customer_name.replace(" ", "_").replace("/", "_").replace("\\", "_")
        
        # Decode and upload to S3 main bucket with new structure
        s3_client = boto3.client('s3')
        file_data = base64.b64decode(file_content)
        
        bucket_name = os.environ['MAIN_BUCKET_NAME']
        s3_key = f"{customer_name_clean}/input/{document_id}_{safe_file_name}"
        
        logger.info(f"Uploading to S3 - bucket: {bucket_name}, key: {s3_key}")
        
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=file_data,
            ContentType=content_type,
            Metadata={
                'document_id': document_id,
                'customer_name': customer_name,
                'upload_timestamp': datetime.now(timezone.utc).isoformat()
            }
        )
        
        s3_uri = f"s3://{bucket_name}/{s3_key}"
        logger.info(f"File uploaded successfully to: {s3_uri}")
        
        # Update DynamoDB with document status
        logger.info("Updating DynamoDB with document status")
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(os.environ['DOCUMENT_STATUS_TABLE'])
        
        current_time = datetime.now(timezone.utc).isoformat()
        
        table.put_item(
            Item={
                'document_id': document_id,
                'status': 'uploaded',
                'file_name': safe_file_name,
                's3_uri': s3_uri,
                'content_type': content_type,
                'customer_name': customer_name,
                'created_at': current_time,
                'updated_at': current_time
            }
        )
        
        logger.info("Document upload completed successfully")
        return {
            'statusCode': 200,
            'headers': get_cors_headers(),
            'body': json.dumps({
                'document_id': document_id,
                's3_uri': s3_uri,
                'customer_name': customer_name,
                'message': 'File uploaded successfully'
            })
        }
        
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'headers': get_cors_headers(),
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }


def get_status(event, context):
    """Get document status and processing results"""
    try:
        logger.info("Starting status check")
        
        # Get document_id from path parameters
        document_id = event.get('pathParameters', {}).get('document_id')
        
        logger.info(f"Status check for document ID: {document_id}")
        
        if not document_id:
            logger.error("Missing document_id parameter")
            return {
                'statusCode': 400,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'document_id is required'})
            }
        
        # Get document status from DynamoDB
        logger.info("Querying DynamoDB for document status")
        dynamodb = boto3.resource('dynamodb')
        doc_table = dynamodb.Table(os.environ['DOCUMENT_STATUS_TABLE'])
        results_table = dynamodb.Table(os.environ['PROCESSING_RESULTS_TABLE'])
        
        # Get document info
        response = doc_table.get_item(Key={'document_id': document_id})
        if 'Item' not in response:
            logger.error(f"Document not found: {document_id}")
            return {
                'statusCode': 404,
                'headers': get_cors_headers(),
                'body': json.dumps({'error': 'Document not found'})
            }
        
        document = response['Item']
        logger.info(f"Document found: {document['file_name']}")
        
        # Get processing results
        logger.info("Querying processing results")
        results_response = results_table.query(
            KeyConditionExpression=Key('document_id').eq(document_id)
        )
        
        processing_results = []
        for item in results_response.get('Items', []):
            processing_results.append({
                'agent_type': item['agent_type'],
                'customer_name': item.get('customer_name'),
                'created_at': item['created_at'],
                'review_available': True
            })
        
        logger.info(f"Found {len(processing_results)} processing results")
        
        response_data = {
            'document_id': document_id,
            'status': document['status'],
            'file_name': document['file_name'],
            's3_uri': document['s3_uri'],
            'customer_name': document.get('customer_name'),
            'created_at': document['created_at'],
            'updated_at': document['updated_at'],
            'processing_results': processing_results
        }
        
        logger.info("Status check completed successfully")
        return {
            'statusCode': 200,
            'headers': get_cors_headers(),
            'body': json.dumps(response_data)
        }
        
    except Exception as e:
        logger.error(f"Error getting status: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'headers': get_cors_headers(),
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }


def list_documents(event, context):
    """List documents with optional filtering"""
    try:
        logger.info("Starting document listing")
        
        # Get optional query parameters
        query_params = event.get('queryStringParameters') or {}
        limit = int(query_params.get('limit', 50))  # Default limit of 50
        customer_name = query_params.get('customer_name')  # Optional customer filter

        logger.info(f"List parameters - limit: {limit}, customer: {customer_name}")

        # Get all documents from DynamoDB (scan)
        logger.info("Scanning DynamoDB for all documents")
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(os.environ['DOCUMENT_STATUS_TABLE'])

        scan_kwargs = {'Limit': limit}
        response = table.scan(**scan_kwargs)

        s3_client = boto3.client('s3')
        documents = []
        for item in response.get('Items', []):
            # Apply customer filter if specified
            if customer_name and item.get('customer_name', '').lower() != customer_name.lower():
                continue
            s3_uri = item.get('s3_uri', '')
            presigned_url = None
            if s3_uri.startswith('s3://'):
                try:
                    # Parse bucket and key
                    _, _, bucket_and_key = s3_uri.partition('s3://')
                    bucket, _, key = bucket_and_key.partition('/')
                    if bucket and key:
                        presigned_url = s3_client.generate_presigned_url(
                            'get_object',
                            Params={'Bucket': bucket, 'Key': key},
                            ExpiresIn=3600
                        )
                except Exception as e:
                    logger.error(f"Error generating presigned URL for {s3_uri}: {str(e)}")
            documents.append({
                'document_id': item['document_id'],
                'file_name': item['file_name'],
                'status': item['status'],
                's3_uri': s3_uri,
                'presigned_url': presigned_url,
                'customer_name': item.get('customer_name'),
                'created_at': item['created_at'],
                'updated_at': item['updated_at']
            })

        logger.info(f"Found {len(documents)} documents")

        response_data = {
            'documents': documents,
            'count': len(documents),
            'customer_filter': customer_name
        }

        logger.info("Document listing completed successfully")
        return {
            'statusCode': 200,
            'headers': get_cors_headers(),
            'body': json.dumps(response_data)
        }
        
    except Exception as e:
        logger.error(f"Error listing documents: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'headers': get_cors_headers(),
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }


def get_cors_headers():
    """Get standard CORS headers"""
    return {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS'
    }

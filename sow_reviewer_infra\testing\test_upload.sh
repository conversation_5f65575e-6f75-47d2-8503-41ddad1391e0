#!/bin/bash

# SOW Reviewer - Upload Test Script
# Tests document upload functionality

set -e

# Configuration
API_URL="${SOW_API_URL:-https://lm3484xfj9.execute-api.us-east-1.amazonaws.com/dev}"
CUSTOMER_NAME="${CUSTOMER_NAME:-Unknown_Customer}"

# Check if file path is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <file_path> [customer_name]"
    echo ""
    echo "Examples:"
    echo "  $0 /path/to/sow_document.txt"
    echo "  $0 /path/to/sow_document.pdf Acme_Corp"
    echo ""
    echo "If no customer_name is provided, it will be extracted from the file (text files only) or use 'Unknown_Customer'"
    exit 1
fi

FILE_PATH="$1"
CUSTOMER_NAME="${2:-$CUSTOMER_NAME}"

# Check if file exists
if [ ! -f "$FILE_PATH" ]; then
    echo "❌ Error: File '$FILE_PATH' not found!"
    exit 1
fi

# Get file name
FILE_NAME=$(basename "$FILE_PATH")

# Detect MIME type (prefer `file` if available); fallback by extension
if command -v file >/dev/null 2>&1; then
    MIME_TYPE=$(file --mime-type -b "$FILE_PATH")
else
    case "${FILE_NAME##*.}" in
        pdf|PDF)
            MIME_TYPE="application/pdf"
            ;;
        txt|TXT)
            MIME_TYPE="text/plain"
            ;;
        *)
            MIME_TYPE="application/octet-stream"
            ;;
    esac
fi

# Enforce allowed types (PDF and TXT only)
if [ "$MIME_TYPE" != "application/pdf" ] && [ "$MIME_TYPE" != "text/plain" ]; then
    echo "❌ Unsupported file type: $MIME_TYPE"
    echo "   Supported types: PDF (application/pdf) and TXT (text/plain)"
    exit 1
fi

# Try to extract customer name from file content only for text files
if [ "$CUSTOMER_NAME" = "Unknown_Customer" ] && [ "$MIME_TYPE" = "text/plain" ]; then
    EXTRACTED_NAME=$(grep -i "^Customer:" "$FILE_PATH" | head -1 | sed 's/.*Customer:\s*//i' | tr -d '\r\n' | xargs || true)
    if [ -n "$EXTRACTED_NAME" ]; then
        CUSTOMER_NAME="$EXTRACTED_NAME"
    fi
fi

# Clean customer name for use in filename
CLEAN_CUSTOMER_NAME=$(echo "$CUSTOMER_NAME" | sed 's/[^a-zA-Z0-9_-]//g')

# Prepare temp workspace
TMP_DIR=$(mktemp -d 2>/dev/null || true)
if [ -z "$TMP_DIR" ]; then
    TMP_DIR="./.tmp_upload_$$"
    mkdir -p "$TMP_DIR"
fi
B64_FILE="$TMP_DIR/file.b64"
PAYLOAD_FILE="$TMP_DIR/payload.json"

# Encode file content to base64 without loading into shell variables
# GNU base64 uses -w 0; on macOS use -b 0. Try -w first, fallback to -b.
BASE64_OK=0
if base64 -w 0 "$FILE_PATH" > "$B64_FILE" 2>/dev/null; then
    BASE64_OK=1
else
    if base64 -b 0 "$FILE_PATH" > "$B64_FILE" 2>/dev/null; then
        BASE64_OK=1
    fi
fi
if [ "$BASE64_OK" -ne 1 ]; then
    echo "❌ Error: base64 encoding failed. Ensure 'base64' utility is available."
    rm -rf "$TMP_DIR"
    exit 1
fi

# Build JSON payload using jq with --rawfile to avoid command length limits
if ! command -v jq >/dev/null 2>&1; then
    echo "❌ Error: 'jq' is required but not installed."
    rm -rf "$TMP_DIR"
    exit 1
fi

jq -n \
  --arg file_name "${CLEAN_CUSTOMER_NAME}_${FILE_NAME}" \
  --arg content_type "$MIME_TYPE" \
  --arg customer_name "$CUSTOMER_NAME" \
  --rawfile file_content "$B64_FILE" \
  '{file_name:$file_name, file_content:$file_content, content_type:$content_type, customer_name:$customer_name}' \
  > "$PAYLOAD_FILE"

echo "=== SOW Reviewer Upload Test ==="
echo "API URL: $API_URL"
echo "File: $FILE_PATH"
echo "Customer: $CUSTOMER_NAME (cleaned: $CLEAN_CUSTOMER_NAME)"
echo "Content-Type: $MIME_TYPE"
echo ""

echo "Uploading file: $FILE_NAME"
echo ""

# Upload document using payload file to avoid argument size limits
RESPONSE=$(curl -s -X POST "$API_URL/upload" \
  -H "Content-Type: application/json" \
  --data-binary @"$PAYLOAD_FILE")

# Clean up temp files
rm -rf "$TMP_DIR"

echo "Response:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

# Extract document ID for further testing
DOCUMENT_ID=$(echo "$RESPONSE" | jq -r '.document_id' 2>/dev/null || echo "")

if [ "$DOCUMENT_ID" != "null" ] && [ -n "$DOCUMENT_ID" ]; then
    echo ""
    echo "✅ Upload successful!"
    echo "Document ID: $DOCUMENT_ID"
    echo ""
    echo "File uploaded: $FILE_NAME"
    echo "Customer: $CUSTOMER_NAME"
    echo ""
    echo "You can now test processing with:"
    echo "  ./test_process.sh $DOCUMENT_ID technical"
    echo "  ./test_process.sh $DOCUMENT_ID financial"  
    echo "  ./test_process.sh $DOCUMENT_ID legal"
    echo ""
    echo "Check status with:"
    echo "  ./test_status.sh $DOCUMENT_ID"
    echo ""
    echo "List documents with:"
    echo "  ./test_list.sh uploaded"
    
    # Save document ID for other scripts
    echo "$DOCUMENT_ID" > .last_document_id
else
    echo ""
    echo "❌ Upload failed for file: $FILE_NAME"
    exit 1
fi

#!/bin/bash

# SOW Reviewer - Complete Test Suite
# Runs all tests in sequence to validate the system

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
API_URL="${SOW_API_URL:-https://x2m7lxjuph.execute-api.us-east-1.amazonaws.com/dev}"
CUSTOMER_NAME="${CUSTOMER_NAME:-Test_Customer}"

echo "=================================================="
echo "            SOW Reviewer - Full Test Suite"
echo "=================================================="
echo "API URL: $API_URL"
echo "Customer: $CUSTOMER_NAME"
echo ""

# Check if required tools are available
command -v curl >/dev/null 2>&1 || { echo "❌ curl is required but not installed."; exit 1; }
command -v jq >/dev/null 2>&1 || { echo "❌ jq is required but not installed."; exit 1; }

echo "✅ Required tools available"
echo ""

# Test 1: List initial documents
echo "=== Test 1: List initial documents ==="
"$SCRIPT_DIR"/test_list.sh uploaded "$CUSTOMER_NAME" || echo "No existing documents found (expected)"
echo ""

# Test 2: Upload document
echo "=== Test 2: Upload test document ==="
CUSTOMER_NAME="$CUSTOMER_NAME" "$SCRIPT_DIR"/test_upload.sh
DOCUMENT_ID=$(cat .last_document_id 2>/dev/null || echo "")
echo ""

if [ -z "$DOCUMENT_ID" ]; then
    echo "❌ Upload test failed - no document ID available"
    exit 1
fi

# Test 3: Check initial status
echo "=== Test 3: Check document status (should be 'uploaded') ==="
"$SCRIPT_DIR"/test_status.sh "$DOCUMENT_ID"
echo ""

# Test 4: Process with technical agent
echo "=== Test 4: Process with technical agent ==="
"$SCRIPT_DIR"/test_process.sh "$DOCUMENT_ID" technical
echo ""

# Wait a bit for processing
echo "Waiting 5 seconds for processing to complete..."
sleep 5
echo ""

# Test 5: Check status after technical processing
echo "=== Test 5: Check status after technical processing ==="
"$SCRIPT_DIR"/test_status.sh "$DOCUMENT_ID"
echo ""

# Test 6: Process with financial agent
echo "=== Test 6: Process with financial agent ==="
"$SCRIPT_DIR"/test_process.sh "$DOCUMENT_ID" financial
echo ""

# Wait a bit for processing
echo "Waiting 5 seconds for processing to complete..."
sleep 5
echo ""

# Test 7: Process with legal agent
echo "=== Test 7: Process with legal agent ==="
"$SCRIPT_DIR"/test_process.sh "$DOCUMENT_ID" legal
echo ""

# Wait a bit for processing
echo "Waiting 5 seconds for processing to complete..."
sleep 5
echo ""

# Test 8: Final status check
echo "=== Test 8: Final status check (should show all processing results) ==="
"$SCRIPT_DIR"/test_status.sh "$DOCUMENT_ID"
echo ""

# Test 9: List documents again
echo "=== Test 9: List all documents ==="
"$SCRIPT_DIR"/test_list.sh uploaded
echo ""

# Test 10: List processed documents
echo "=== Test 10: List processed documents ==="
"$SCRIPT_DIR"/test_list.sh processed_legal
echo ""

echo "=================================================="
echo "                  Test Summary"
echo "=================================================="
echo "✅ Upload test: PASSED"
echo "✅ Status check: PASSED"
echo "✅ Technical processing: COMPLETED"
echo "✅ Financial processing: COMPLETED"
echo "✅ Legal processing: COMPLETED"
echo "✅ Document listing: PASSED"
echo ""
echo "🎉 All tests completed successfully!"
echo ""
echo "Document ID for manual testing: $DOCUMENT_ID"
echo ""
echo "Next steps:"
echo "1. Check S3 bucket for output files under: $CUSTOMER_NAME/output/"
echo "2. Review CloudWatch logs for detailed processing information:"
echo "   - Agent Operations: /aws/lambda/{env}-sow-agent-operations"
echo "   - General Operations: /aws/lambda/{env}-sow-general-operations"
echo "3. Test with different document types and customers"
echo ""
echo "Manual test commands:"
echo "  ./test_upload.sh"
echo "  ./test_process.sh \$DOCUMENT_ID technical"
echo "  ./test_status.sh \$DOCUMENT_ID"
echo "  ./test_list.sh uploaded MyCustomer"

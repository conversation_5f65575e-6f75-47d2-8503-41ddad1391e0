variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "project_name" {
  description = "Project name used in resource naming"
  type        = string
  default     = "sow-reviewer"
}

variable "name_prefix" {
  description = "Prefix for all resource names to avoid conflicts"
  type        = string
  default     = ""
}

variable "upload_handler_function_name" {
  description = "Name of the upload handler <PERSON><PERSON> function"
  type        = string
}

variable "upload_handler_invoke_arn" {
  description = "Invoke ARN of the upload handler Lambda function"
  type        = string
}

variable "agent_handler_function_name" {
  description = "Name of the agent handler Lambda function"
  type        = string
}

variable "agent_handler_invoke_arn" {
  description = "Invoke ARN of the agent handler <PERSON><PERSON> function"
  type        = string
}

variable "status_handler_function_name" {
  description = "Name of the status handler Lamb<PERSON> function"
  type        = string
}

variable "status_handler_invoke_arn" {
  description = "Invoke ARN of the status handler Lambda function"
  type        = string
}

variable "list_handler_function_name" {
  description = "Name of the list handler Lambda function"
  type        = string
}

variable "list_handler_invoke_arn" {
  description = "Invoke <PERSON><PERSON> of the list handler Lambda function"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

# manually generated from https://www.unicode.org/Public/MAPPINGS/VENDORS/ADOBE/symbol.txt
_symbol_encoding = [
    "\u0000",
    "\u0001",
    "\u0002",
    "\u0003",
    "\u0004",
    "\u0005",
    "\u0006",
    "\u0007",
    "\u0008",
    "\u0009",
    "\u000A",
    "\u000B",
    "\u000C",
    "\u000D",
    "\u000E",
    "\u000F",
    "\u0010",
    "\u0011",
    "\u0012",
    "\u0013",
    "\u0014",
    "\u0015",
    "\u0016",
    "\u0017",
    "\u0018",
    "\u0019",
    "\u001A",
    "\u001B",
    "\u001C",
    "\u001D",
    "\u001E",
    "\u001F",
    "\u0020",
    "\u0021",
    "\u2200",
    "\u0023",
    "\u2203",
    "\u0025",
    "\u0026",
    "\u220B",
    "\u0028",
    "\u0029",
    "\u2217",
    "\u002B",
    "\u002C",
    "\u2212",
    "\u002E",
    "\u002F",
    "\u0030",
    "\u0031",
    "\u0032",
    "\u0033",
    "\u0034",
    "\u0035",
    "\u0036",
    "\u0037",
    "\u0038",
    "\u0039",
    "\u003A",
    "\u003B",
    "\u003C",
    "\u003D",
    "\u003E",
    "\u003F",
    "\u2245",
    "\u0391",
    "\u0392",
    "\u03A7",
    "\u0394",
    "\u0395",
    "\u03A6",
    "\u0393",
    "\u0397",
    "\u0399",
    "\u03D1",
    "\u039A",
    "\u039B",
    "\u039C",
    "\u039D",
    "\u039F",
    "\u03A0",
    "\u0398",
    "\u03A1",
    "\u03A3",
    "\u03A4",
    "\u03A5",
    "\u03C2",
    "\u03A9",
    "\u039E",
    "\u03A8",
    "\u0396",
    "\u005B",
    "\u2234",
    "\u005D",
    "\u22A5",
    "\u005F",
    "\uF8E5",
    "\u03B1",
    "\u03B2",
    "\u03C7",
    "\u03B4",
    "\u03B5",
    "\u03C6",
    "\u03B3",
    "\u03B7",
    "\u03B9",
    "\u03D5",
    "\u03BA",
    "\u03BB",
    "\u00B5",
    "\u03BD",
    "\u03BF",
    "\u03C0",
    "\u03B8",
    "\u03C1",
    "\u03C3",
    "\u03C4",
    "\u03C5",
    "\u03D6",
    "\u03C9",
    "\u03BE",
    "\u03C8",
    "\u03B6",
    "\u007B",
    "\u007C",
    "\u007D",
    "\u223C",
    "\u007F",
    "\u0080",
    "\u0081",
    "\u0082",
    "\u0083",
    "\u0084",
    "\u0085",
    "\u0086",
    "\u0087",
    "\u0088",
    "\u0089",
    "\u008A",
    "\u008B",
    "\u008C",
    "\u008D",
    "\u008E",
    "\u008F",
    "\u0090",
    "\u0091",
    "\u0092",
    "\u0093",
    "\u0094",
    "\u0095",
    "\u0096",
    "\u0097",
    "\u0098",
    "\u0099",
    "\u009A",
    "\u009B",
    "\u009C",
    "\u009D",
    "\u009E",
    "\u009F",
    "\u20AC",
    "\u03D2",
    "\u2032",
    "\u2264",
    "\u2044",
    "\u221E",
    "\u0192",
    "\u2663",
    "\u2666",
    "\u2665",
    "\u2660",
    "\u2194",
    "\u2190",
    "\u2191",
    "\u2192",
    "\u2193",
    "\u00B0",
    "\u00B1",
    "\u2033",
    "\u2265",
    "\u00D7",
    "\u221D",
    "\u2202",
    "\u2022",
    "\u00F7",
    "\u2260",
    "\u2261",
    "\u2248",
    "\u2026",
    "\uF8E6",
    "\uF8E7",
    "\u21B5",
    "\u2135",
    "\u2111",
    "\u211C",
    "\u2118",
    "\u2297",
    "\u2295",
    "\u2205",
    "\u2229",
    "\u222A",
    "\u2283",
    "\u2287",
    "\u2284",
    "\u2282",
    "\u2286",
    "\u2208",
    "\u2209",
    "\u2220",
    "\u2207",
    "\uF6DA",
    "\uF6D9",
    "\uF6DB",
    "\u220F",
    "\u221A",
    "\u22C5",
    "\u00AC",
    "\u2227",
    "\u2228",
    "\u21D4",
    "\u21D0",
    "\u21D1",
    "\u21D2",
    "\u21D3",
    "\u25CA",
    "\u2329",
    "\uF8E8",
    "\uF8E9",
    "\uF8EA",
    "\u2211",
    "\uF8EB",
    "\uF8EC",
    "\uF8ED",
    "\uF8EE",
    "\uF8EF",
    "\uF8F0",
    "\uF8F1",
    "\uF8F2",
    "\uF8F3",
    "\uF8F4",
    "\u00F0",
    "\u232A",
    "\u222B",
    "\u2320",
    "\uF8F5",
    "\u2321",
    "\uF8F6",
    "\uF8F7",
    "\uF8F8",
    "\uF8F9",
    "\uF8FA",
    "\uF8FB",
    "\uF8FC",
    "\uF8FD",
    "\uF8FE",
    "\u00FF",
]
assert len(_symbol_encoding) == 256

#!/bin/bash

# SOW Reviewer - Process Test Script
# Tests document processing with Bedrock agents

set -e

# Configuration
API_URL="${SOW_API_URL:-https://lm3484xfj9.execute-api.us-east-1.amazonaws.com/dev}"

echo "=== SOW Reviewer Process Test ==="
echo "API URL: $API_URL"
echo ""

# Get parameters
DOCUMENT_ID="$1"
AGENT_TYPE="$2"

# Use last document ID if not provided
if [ -z "$DOCUMENT_ID" ] && [ -f ".last_document_id" ]; then
    DOCUMENT_ID=$(cat .last_document_id)
fi

# Validate parameters
if [ -z "$DOCUMENT_ID" ]; then
    echo "❌ Document ID required!"
    echo "Usage: $0 <document_id> [agent_type]"
    echo "Available agent types: technical, financial, legal"
    echo ""
    echo "Or run ./test_upload.sh first to create a test document"
    exit 1
fi

if [ -z "$AGENT_TYPE" ]; then
    AGENT_TYPE="technical"
fi

if [[ ! "$AGENT_TYPE" =~ ^(technical|financial|legal)$ ]]; then
    echo "❌ Invalid agent type: $AGENT_TYPE"
    echo "Available agent types: technical, financial, legal"
    exit 1
fi

echo "Document ID: $DOCUMENT_ID"
echo "Agent Type: $AGENT_TYPE"
echo ""

# Create process payload
PROCESS_PAYLOAD=$(cat << EOF
{
  "document_id": "$DOCUMENT_ID",
  "agent_type": "$AGENT_TYPE"
}
EOF
)

echo "Processing document with $AGENT_TYPE agent..."
echo ""

# Process document
RESPONSE=$(curl -s -X POST "$API_URL/process" \
  -H "Content-Type: application/json" \
  -d "$PROCESS_PAYLOAD")

echo "Response:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

# Check if successful
SUCCESS=$(echo "$RESPONSE" | jq -r '.message' 2>/dev/null || echo "")

if [[ "$SUCCESS" == *"completed successfully"* ]]; then
    echo ""
    echo "✅ Processing successful!"
    echo ""
    echo "Check processing status with:"
    echo "  ./test_status.sh $DOCUMENT_ID"
    echo ""
    echo "Test other agent types:"
    if [ "$AGENT_TYPE" != "technical" ]; then
        echo "  ./test_process.sh $DOCUMENT_ID technical"
    fi
    if [ "$AGENT_TYPE" != "financial" ]; then
        echo "  ./test_process.sh $DOCUMENT_ID financial"
    fi
    if [ "$AGENT_TYPE" != "legal" ]; then
        echo "  ./test_process.sh $DOCUMENT_ID legal"
    fi
else
    echo ""
    echo "❌ Processing failed!"
    echo "Check the response above for error details"
    exit 1
fi

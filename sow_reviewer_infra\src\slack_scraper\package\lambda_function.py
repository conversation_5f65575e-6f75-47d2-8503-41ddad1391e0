import os
import json
import time
import boto3
import hashlib
import re
from datetime import datetime, timezone
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

s3 = boto3.client("s3")
ddb = boto3.resource("dynamodb")

SLACK_BOT_TOKEN = os.environ.get("SLACK_BOT_TOKEN")
SLACK_WORKSPACE_ID = os.environ.get("SLACK_WORKSPACE_ID") or "default"
SLACK_S3_BUCKET = os.environ["SLACK_S3_BUCKET"]
SLACK_METADATA_TABLE = os.environ["SLACK_METADATA_TABLE"]
SLACK_CHANNEL_CACHE_TABLE = os.environ["SLACK_CHANNEL_CACHE_TABLE"]

client = WebClient(token=SLACK_BOT_TOKEN)
meta_table = ddb.Table(SLACK_METADATA_TABLE)
channel_cache_table = ddb.Table(SLACK_CHANNEL_CACHE_TABLE)

# Rate limiting configuration
RATE_LIMIT_DELAY = 1.1  # seconds between API calls
BATCH_SIZE = 100  # messages per batch
MAX_RETRIES = 3

# Global user cache for the current channel being processed
_user_cache = {}


def _get_last_timestamp(channel_id: str) -> str:
    """Get the latest timestamp from DynamoDB for incremental fetching"""
    try:
        resp = meta_table.get_item(Key={"workspace_id": SLACK_WORKSPACE_ID, "channel_id": channel_id})
        item = resp.get("Item")
        return item.get("last_ts", "0") if item else "0"
    except Exception as e:
        print(f"Error getting last timestamp for channel {channel_id}: {e}")
        return "0"


def _update_last_timestamp(channel_id: str, channel_name: str, latest_ts: str):
    """Update the latest timestamp in DynamoDB"""
    try:
        meta_table.put_item(Item={
            "workspace_id": SLACK_WORKSPACE_ID,
            "channel_id": channel_id,
            "channel_name": channel_name,
            "last_ts": latest_ts,
            "updated_at": datetime.now(timezone.utc).isoformat()
        })
    except Exception as e:
        print(f"Error updating last timestamp for channel {channel_id}: {e}")


def _get_user_name(user_id: str) -> str:
    """Get user name from user ID using the global user cache"""
    if not user_id:
        return "Unknown User"
    
    # Check if user is in cache
    if user_id in _user_cache:
        return _user_cache[user_id]
    
    # Fetch user info and add to cache
    try:
        user_info = _rate_limited_api_call(client.users_info, user=user_id)
        user_name = user_info["user"]["name"]
        _user_cache[user_id] = user_name  # Add to cache for future use
        return user_name
    except SlackApiError as e:
        print(f"Error fetching user info for {user_id}: {e}")
        return "Unknown User"
    except Exception as e:
        print(f"Unexpected error fetching user info for {user_id}: {e}")
        return "Unknown User"


def _parse_timestamp(ts: str) -> str:
    """Parse Slack timestamp to readable format"""
    try:
        return datetime.fromtimestamp(float(ts)).strftime('%Y-%m-%d %H:%M:%S')
    except:
        return ts


def _get_string_hash(text: str) -> str:
    """Generate MD5 hash for text"""
    hash_object = hashlib.md5(text.encode('utf-8'))
    return hash_object.hexdigest()


def _sanitize_filename(name: str) -> str:
    """Sanitize filename by removing special characters"""
    return re.sub(r'[^a-zA-Z0-9_-]', '_', name)


def _get_thread_messages(channel_id: str, ts: str) -> dict:
    """Fetch thread messages for a given timestamp"""
    print(f"Fetching thread messages for channel: {channel_id}, timestamp: {ts}")
    thread_info = {
        "parent_message": None,
        "replies": []
    }
    
    try:
        thread_replies = _rate_limited_api_call(
            client.conversations_replies, 
            channel=channel_id, 
            ts=ts
        )
        
        for idx, message in enumerate(thread_replies["messages"]):
            user_name = _get_user_name(message.get("user", ""))
            timestamp = _parse_timestamp(message["ts"])
            message_info = {
                "user": user_name,
                "timestamp": timestamp,
                "text": message.get("text", ""),
                "attachments": message.get("attachments", []),
                "files": message.get("files", [])
            }
            
            if idx == 0:
                thread_info["parent_message"] = message_info
            else:
                thread_info["replies"].append(message_info)
                
        print(f"Thread info fetched: {len(thread_info['replies'])} replies")
        return thread_info
        
    except SlackApiError as e:
        print(f"Error fetching thread messages: {e}")
        return thread_info


def _write_messages_to_s3(channel_id: str, channel_name: str, messages: list):
    if not messages:
        return
    try:
        date_prefix = datetime.now(timezone.utc).strftime("%Y/%m/%d")
        sanitized_channel_name = _sanitize_filename(channel_name)
        key = f"slack/{SLACK_WORKSPACE_ID}/channels/{channel_id}_{sanitized_channel_name}/date={date_prefix}/{int(time.time())}.txt"
        
        # Parse messages to text format
        text_content = []
        for message in messages:
            parsed_text, _, _ = parse_messages(message)
            text_content.append(parsed_text)
        
        # Reverse the order so older messages appear first (chronological order)
        text_content.reverse()
        
        # Join all parsed messages with separators
        full_content = "\n\n" + "="*80 + "\n\n".join(text_content)
        body = full_content.encode("utf-8")
        
        s3.put_object(Bucket=SLACK_S3_BUCKET, Key=key, Body=body, ContentType="text/plain")
        print(f"Wrote {len(messages)} messages to S3: {key}")
    except Exception as e:
        print(f"Error writing messages to S3 for channel {channel_id}: {e}")


def parse_messages(message_data):
    """Parse message data into readable text format"""
    parsed_data = []
    raw_list = []
    
    # Handle both string and list formats for attachments and files
    attachments = message_data.get('attachments', [])
    if isinstance(attachments, str):
        try:
            attachments = json.loads(attachments)
        except:
            attachments = []
    
    files = message_data.get('files', [])
    if isinstance(files, str):
        try:
            files = json.loads(files)
        except:
            files = []
    
    parent_message = {
        'channel': message_data.get('channel', ''),
        'message': message_data.get('text', ''),
        'message_links': [att.get('title_link') for att in attachments if att.get('title_link')],
        'message_files': [file.get('permalink') for file in files if file.get('permalink')],
        'message_sender': message_data.get('user', ''),
        'message_time': message_data.get('timestamp', ''),
        'id': message_data.get('id', '')
    }
    
    # parsed_data.append(f"Channel: {parent_message['channel']}")
    parsed_data.append(f"Time: {parent_message['message_time']}")
    parsed_data.append(f"User: {parent_message['message_sender']}")
    parsed_data.append(f"Message: {parent_message['message']}")
    if parent_message['message_links']:
        parsed_data.append(f"Links: {', '.join(parent_message['message_links'])}")
    if parent_message['message_files']:
        parsed_data.append(f"Files: {', '.join(parent_message['message_files'])}")
    
    raw_list.append(parent_message)
    
    # Handle replies
    replies = message_data.get('replies', [])
    if isinstance(replies, str):
        try:
            replies = json.loads(replies)
        except:
            replies = []
    
    for index, reply in enumerate(replies, start=1):
        reply_message = {
            'message': reply.get('text', ''),
            'message_links': [att.get('title_link') for att in reply.get('attachments', []) if att.get('title_link')],
            'message_files': [file.get('permalink') for file in reply.get('files', []) if file.get('permalink')],
            'message_sender': reply.get('user', ''),
            'message_time': reply.get('timestamp', '')
        }
        parsed_data.append(f"  [Reply {index}]")
        parsed_data.append(f"    Time: {reply_message['message_time']}")
        parsed_data.append(f"    User: {reply_message['message_sender']}")
        parsed_data.append(f"    Message: {reply_message['message']}")
        if reply_message['message_links']:
            parsed_data.append(f"    Links: {', '.join(reply_message['message_links'])}")
        if reply_message['message_files']:
            parsed_data.append(f"    Files: {', '.join(reply_message['message_files'])}")
        raw_list.append(reply_message)
    
    return '\n'.join(parsed_data), parent_message['channel'], raw_list


def _parse_message(message: dict, channel_name: str) -> dict:
    """Parse and enrich a Slack message with additional data"""
    user_name = _get_user_name(message.get("user", ""))
    timestamp = message["ts"]
    parsed_timestamp = _parse_timestamp(timestamp)
    
    # Extract links from attachments
    message_links = []
    for att in message.get("attachments", []):
        if att.get("title_link"):
            message_links.append(att["title_link"])
    
    # Extract file links
    message_files = []
    for file in message.get("files", []):
        if file.get("permalink"):
            message_files.append(file["permalink"])
    
    # Check if message is part of a thread
    is_thread = "thread_ts" in message
    thread_replies = []
    
    # Get thread data if this is a thread parent
    if is_thread and message.get("thread_ts") == message.get("ts"):
        thread_data = _get_thread_messages(message.get("channel", ""), message["ts"])
        thread_replies = thread_data.get("replies", [])
    
    # Create enriched message data
    message_data = {
        'id': str(timestamp),
        'channel': channel_name,
        'channel_id': message.get("channel", ""),
        'timestamp': parsed_timestamp,
        'raw_timestamp': timestamp,
        'user': user_name,
        'user_id': message.get("user", ""),
        'text': message.get("text", ""),
        'attachments': message.get("attachments", []),
        'files': message.get("files", []),
        'message_links': message_links,
        'message_files': message_files,
        'is_thread': is_thread,
        'thread_ts': message.get("thread_ts"),
        'replies': thread_replies,
        'reply_count': message.get("reply_count", 0),
        'reactions': message.get("reactions", []),
        'edited': message.get("edited", {}),
        'insertion_time': datetime.now(timezone.utc).isoformat(),
    }
    
    # Generate hash for deduplication
    message_text = f"{message_data['text']}{''.join(message_links)}{''.join(message_files)}"
    message_data['message_hash'] = _get_string_hash(message_text)
    
    return message_data


def _get_cached_channels():
    """Get channels from cache"""
    try:
        response = channel_cache_table.query(
            KeyConditionExpression="workspace_id = :workspace_id",
            ExpressionAttributeValues={":workspace_id": SLACK_WORKSPACE_ID}
        )
        return response.get("Items", [])
    except Exception as e:
        print(f"Error getting cached channels: {e}")
        return []


def _cache_channels(channels):
    """Cache channel information"""
    try:
        with channel_cache_table.batch_writer() as batch:
            for channel in channels:
                batch.put_item(Item={
                    "workspace_id": SLACK_WORKSPACE_ID,
                    "channel_id": channel.get("id"),
                    "channel_name": channel.get("name", ""),
                    "is_member": channel.get("is_member", False),
                    "is_private": channel.get("is_private", False),
                    "is_archived": channel.get("is_archived", False),
                    "member_count": channel.get("num_members", 0),
                    "last_updated": datetime.now(timezone.utc).isoformat()
                })
        print(f"Cached {len(channels)} channels")
    except Exception as e:
        print(f"Error caching channels: {e}")


def _update_channel_membership(channel_id, is_member):
    """Update channel membership status"""
    try:
        channel_cache_table.update_item(
            Key={
                "workspace_id": SLACK_WORKSPACE_ID,
                "channel_id": channel_id
            },
            UpdateExpression="SET is_member = :is_member, last_updated = :last_updated",
            ExpressionAttributeValues={
                ":is_member": is_member,
                ":last_updated": datetime.now(timezone.utc).isoformat()
            }
        )
    except Exception as e:
        print(f"Error updating channel membership for {channel_id}: {e}")


def _rate_limited_api_call(func, *args, **kwargs):
    """Make API call with rate limiting and retries"""
    for attempt in range(MAX_RETRIES):
        try:
            result = func(*args, **kwargs)
            time.sleep(RATE_LIMIT_DELAY)  # Rate limiting delay
            return result
        except SlackApiError as e:
            if e.response["error"] == "ratelimited":
                retry_after = int(e.response.get("headers", {}).get("Retry-After", 60))
                print(f"Rate limited, waiting {retry_after} seconds...")
                time.sleep(retry_after)
                continue
            elif e.response["error"] == "not_in_channel":
                print(f"Not in channel, skipping...")
                raise
            else:
                print(f"Slack API error: {e.response['error']}")
                if attempt == MAX_RETRIES - 1:
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff
        except Exception as e:
            print(f"Unexpected error: {e}")
            if attempt == MAX_RETRIES - 1:
                raise
            time.sleep(2 ** attempt)
    raise Exception("Max retries exceeded")


def scrape_channel(channel_id: str, channel_name: str = ""):
    print(f"Starting to scrape channel {channel_id} ({channel_name})")
    last_timestamp = _get_last_timestamp(channel_id)
    print(f"Starting from timestamp: {last_timestamp}")
    
    has_more = True
    latest_ts = last_timestamp
    all_messages = []
    total_messages = 0
    
    while has_more:
        try:
            # Use oldest parameter to start from the last timestamp
            resp = _rate_limited_api_call(
                client.conversations_history, 
                channel=channel_id, 
                oldest=last_timestamp,
                limit=BATCH_SIZE
            )
        except SlackApiError as e:
            if e.response["error"] == "not_in_channel":
                print(f"Not in channel {channel_id} ({channel_name}), skipping")
                return
            else:
                print(f"Slack error on channel {channel_id} ({channel_name}): {e.response['error']}")
                break
        
        messages = resp.get("messages", [])
        if messages:
            # Update latest timestamp to the most recent message
            latest_ts = max(latest_ts, messages[0].get("ts", latest_ts))
            
            # Parse and enrich each message
            parsed_messages = []
            for message in messages:
                # Add channel info to message for thread processing
                message["channel"] = channel_id
                parsed_message = _parse_message(message, channel_name)
                parsed_messages.append(parsed_message)
            
            all_messages.extend(parsed_messages)
            total_messages += len(messages)
            
            # Write to S3 when we have enough messages
            if len(all_messages) >= 1000:
                _write_messages_to_s3(channel_id, channel_name, all_messages)
                all_messages = []
        
        # Check if there are more messages
        has_more = resp.get("has_more", False)
        if has_more:
            # Get the cursor for the next page
            cursor = resp.get("response_metadata", {}).get("next_cursor")
            if cursor:
                # Continue with cursor-based pagination
                while has_more:
                    try:
                        resp = _rate_limited_api_call(
                            client.conversations_history, 
                            channel=channel_id, 
                            cursor=cursor,
                            limit=BATCH_SIZE
                        )
                    except SlackApiError as e:
                        print(f"Error in cursor-based pagination: {e}")
                        break
                    
                    messages = resp.get("messages", [])
                    if messages:
                        latest_ts = max(latest_ts, messages[0].get("ts", latest_ts))
                        
                        parsed_messages = []
                        for message in messages:
                            message["channel"] = channel_id
                            parsed_message = _parse_message(message, channel_name)
                            parsed_messages.append(parsed_message)
                        
                        all_messages.extend(parsed_messages)
                        total_messages += len(messages)
                        
                        if len(all_messages) >= 1000:
                            _write_messages_to_s3(channel_id, channel_name, all_messages)
                            all_messages = []
                    
                    cursor = resp.get("response_metadata", {}).get("next_cursor")
                    has_more = bool(cursor)
            else:
                has_more = False
    
    # Write remaining messages
    if all_messages:
        _write_messages_to_s3(channel_id, channel_name, all_messages)
    
    # Update the latest timestamp in DynamoDB
    if latest_ts != last_timestamp:
        _update_last_timestamp(channel_id, channel_name, latest_ts)
        print(f"Updated last timestamp from {last_timestamp} to {latest_ts}")
    
    print(f"Finished scraping channel {channel_id} ({channel_name}): {total_messages} messages")


def lambda_handler(event, context):
    """
    Event payload options:
    - channels_source: "cache" | "api" (optional)
        - cache (default): use only cached channels; do not call Slack API if cache is empty
        - api: fetch channels from Slack API and refresh cache

    """
    print(f"Starting Slack scraper at {datetime.now(timezone.utc).isoformat()}")
    
    if not SLACK_BOT_TOKEN:
        raise RuntimeError("SLACK_BOT_TOKEN is not configured")

    # Determine source of channels: "cache" (default) or "api"
    channels_source = (event.get("channels_source") or "cache").lower()
    if channels_source not in ("cache", "api"):
        channels_source = "cache"

    # Check if we have cached channels
    cached_channels = _get_cached_channels()
    print(f"Found {len(cached_channels)} cached channels")

    # Deprecated: force_refresh ignored (auto mode removed)
    force_refresh = False

    if channels_source == "api":
        print("channels_source=api: Fetching channels from Slack API...")
        all_channels = _fetch_channels_from_api()
        _cache_channels(all_channels)
        cached_channels = _get_cached_channels()
    elif channels_source == "cache":
        print("channels_source=cache: Using cached channels only")
        if not cached_channels:
            print("No cached channels found and channels_source=cache; exiting without API call.")
            return {
                "status": "ok",
                "channels_processed": 0,
                "total_channels_found": 0,
                "accessible_channels": 0,
                "cache_used": True
            }
        all_channels = cached_channels
    else:
        # cache mode (default): use cached channels only
        print("channels_source=cache: Using cached channels")
        all_channels = cached_channels

    # Filter channels where the bot is a member
    accessible_channels = []
    for ch in cached_channels:
        ch_id = ch.get("channel_id")
        ch_name = ch.get("channel_name", "")
        is_member = ch.get("is_member", False)
        
        if is_member:
            accessible_channels.append(ch)
            print(f"Channel accessible: {ch_name} ({ch_id})")
        else:
            print(f"Channel not accessible (not a member): {ch_name} ({ch_id})")

    print(f"Found {len(accessible_channels)} accessible channels out of {len(cached_channels)} total channels")

    # Scrape accessible channels and update membership status
    processed_channels = 0
    for ch in accessible_channels:
        ch_id = ch.get("channel_id")
        ch_name = ch.get("channel_name", "")
        
        if ch_id:
            try:
                scrape_channel(ch_id, ch_name)
                processed_channels += 1
                # Update membership status to True (confirmed accessible)
                _update_channel_membership(ch_id, True)
            except SlackApiError as e:
                if e.response["error"] == "not_in_channel":
                    print(f"Channel {ch_name} ({ch_id}) no longer accessible, updating cache")
                    _update_channel_membership(ch_id, False)
                else:
                    print(f"Error scraping channel {ch_name} ({ch_id}): {e}")
                continue
            except Exception as e:
                print(f"Error scraping channel {ch_name} ({ch_id}): {e}")
                continue

    print(f"Slack scraper completed at {datetime.now(timezone.utc).isoformat()}")
    return {
        "status": "ok", 
        "channels_processed": processed_channels,
        "total_channels_found": len(cached_channels),
        "accessible_channels": len(accessible_channels),
        "cache_used": channels_source == "cache"
    }


def _fetch_channels_from_api():
    """Fetch channels from Slack API"""
    next_cursor = None
    all_channels = []
    
    while True:
        try:
            resp = _rate_limited_api_call(
                client.conversations_list, 
                cursor=next_cursor, 
                limit=200, 
                types="public_channel,private_channel"
            )
        except SlackApiError as e:
            print(f"Failed to list channels: {e.response['error']}")
            break
        
        channels = resp.get("channels", [])
        all_channels.extend(channels)
        print(f"Fetched {len(channels)} channels (total: {len(all_channels)})")
        
        next_cursor = resp.get("response_metadata", {}).get("next_cursor") or None
        if not next_cursor:
            break
    
    return all_channels

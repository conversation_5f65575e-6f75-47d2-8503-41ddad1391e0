variable "region" {
  description = "AWS region to deploy resources in"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "foundation_model_arn" {
  description = "ARN of the foundation model to use for Bedrock agents"
  type        = string
  default     = null
}

variable "default_tags" {
  description = "Default tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# Naming variables to avoid conflicts
variable "project_name" {
  description = "Project name used in resource naming"
  type        = string
  default     = "sow-reviewer"
}

variable "resource_prefix" {
  description = "Prefix for all resource names to avoid conflicts"
  type        = string
  default     = null
}

variable "frontend_domain_name" {
  description = "Domain name for the frontend (e.g., sowreviewer.protagona.com)"
  type        = string
  default     = "sowreviewer.protagona.com"
}

variable "cognito_domain_prefix" {
  description = "Prefix for Cognito user pool domain"
  type        = string
  default     = "sowreviewer-frontend-domain"
}

variable "test_user_username" {
  description = "Username for the test user created in Cognito"
  type        = string
  default     = "testuser1"
}

variable "test_user_email" {
  description = "Email for the test user created in Cognito"
  type        = string
  default     = "<EMAIL>"
}

variable "test_user_password" {
  description = "Temporary password for the test user"
  type        = string
  default     = "TestUser123!"
  sensitive   = true
}

variable "acm_certificate_arn" {
  description = "ARN of the ACM certificate for the frontend domain"
  type        = string
  default     = "arn:aws:acm:us-east-1:339712708985:certificate/82aa912c-3dc4-489b-bbbf-4fcc088c2c3a"
}

variable "geo_restriction_locations" {
  description = "List of country codes for CloudFront geo-restriction"
  type        = list(string)
  default     = ["US", "CA", "GB", "DE", "EG"]
}

variable "cloudfront_price_class" {
  description = "CloudFront price class"
  type        = string
  default     = "PriceClass_200"
  validation {
    condition     = contains(["PriceClass_100", "PriceClass_200", "PriceClass_All"], var.cloudfront_price_class)
    error_message = "CloudFront price class must be one of: PriceClass_100, PriceClass_200, PriceClass_All."
  }
}

# Slack configuration (set via tfvars or environment)
variable "slack_bot_token" {
  description = "Slack Bot OAuth token (xoxb-...)"
  type        = string
  default     = null
  sensitive   = true
}

variable "slack_app_token" {
  description = "Slack App Level token (xapp-...) if needed"
  type        = string
  default     = null
  sensitive   = true
}

variable "slack_workspace_id" {
  description = "Slack workspace/team ID"
  type        = string
  default     = null
}

# S3 Vectors configuration
variable "s3_vectors_bucket_name" {
  description = "Name of the S3 Vectors bucket for channel similarity search (optional, defaults to generated name)"
  type        = string
  default     = null
}

variable "s3_vectors_index_name" {
  description = "Name of the S3 Vectors index for channel summaries"
  type        = string
  default     = "channel-summaries"
}


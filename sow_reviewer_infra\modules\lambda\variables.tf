variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "project_name" {
  description = "Project name used in resource naming"
  type        = string
  default     = "sow-reviewer"
}

variable "name_prefix" {
  description = "Prefix for all resource names to avoid conflicts"
  type        = string
  default     = ""
}

variable "main_bucket_name" {
  description = "Name of the main S3 bucket for SOW operations"
  type        = string
}

variable "main_bucket_arn" {
  description = "ARN of the main S3 bucket for SOW operations"
  type        = string
}

# Backward compatibility variables
variable "documents_bucket_name" {
  description = "Name of the S3 bucket for documents (backward compatibility)"
  type        = string
  default     = null
}

variable "documents_bucket_arn" {
  description = "ARN of the S3 bucket for documents (backward compatibility)"
  type        = string
  default     = null
}

variable "reviews_bucket_name" {
  description = "Name of the S3 bucket for reviews (backward compatibility)"
  type        = string
  default     = null
}

variable "reviews_bucket_arn" {
  description = "ARN of the S3 bucket for reviews (backward compatibility)"
  type        = string
  default     = null
}

variable "slack_bucket_name" {
  description = "Name of the Slack S3 bucket"
  type        = string
}

variable "slack_bucket_arn" {
  description = "ARN of the Slack S3 bucket"
  type        = string
}

variable "document_status_table_name" {
  description = "Name of the document status DynamoDB table"
  type        = string
}

variable "document_status_table_arn" {
  description = "ARN of the document status DynamoDB table"
  type        = string
}

variable "processing_results_table_name" {
  description = "Name of the processing results DynamoDB table"
  type        = string
}

variable "processing_results_table_arn" {
  description = "ARN of the processing results DynamoDB table"
  type        = string
}

variable "slack_metadata_table_name" {
  description = "Name of the Slack metadata DynamoDB table"
  type        = string
}

variable "slack_metadata_table_arn" {
  description = "ARN of the Slack metadata DynamoDB table"
  type        = string
}

variable "slack_channel_cache_table_name" {
  description = "Name of the Slack channel cache DynamoDB table"
  type        = string
}

variable "slack_channel_cache_table_arn" {
  description = "ARN of the Slack channel cache DynamoDB table"
  type        = string
}

variable "slack_channel_reviews_table_name" {
  description = "Name of the Slack channel reviews DynamoDB table"
  type        = string
}

variable "slack_channel_reviews_table_arn" {
  description = "ARN of the Slack channel reviews DynamoDB table"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

# Slack variables (provided via root module variables/locals or TFVARS)
variable "slack_bot_token" {
  description = "Slack Bot OAuth token (xoxb-...)"
  type        = string
  default     = null
  sensitive   = true
}

variable "slack_app_token" {
  description = "Slack App Level token (xapp-...) if needed"
  type        = string
  default     = null
  sensitive   = true
}

variable "slack_workspace_id" {
  description = "Slack workspace/team ID"
  type        = string
  default     = null
}

variable "log_retention_in_days" {
  description = "Number of days to retain CloudWatch logs"
  type        = number
  default     = 14
}

# S3 Vectors variables
variable "s3_vectors_bucket_name" {
  description = "Name of the S3 Vectors bucket for channel similarity search"
  type        = string
  default     = null
}

variable "s3_vectors_index_name" {
  description = "Name of the S3 Vectors index for channel summaries"
  type        = string
  default     = "channel-summaries"
}

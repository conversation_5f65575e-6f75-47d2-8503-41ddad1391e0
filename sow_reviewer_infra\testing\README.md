# SOW Reviewer Testing

This directory contains scripts to validate the deployed SOW Reviewer API end-to-end.

## Prerequisites
- curl
- jq
- Bash environment (Linux/Mac, or Windows via Git Bash/WSL)

Optional on Windows: PowerShell is fine for Terraform, but run these bash scripts in Git Bash/WSL.

## Environment
Set your API base URL (the `/dev` or target stage base):
```bash
export SOW_API_URL="https://<api-id>.execute-api.<region>.amazonaws.com/dev"
```

## Files
- `test_all.sh`: Runs the full suite in sequence
- `test_upload.sh`: Uploads a document (now supports real files)
- `test_process.sh`: Triggers agent processing: technical | financial | legal
- `test_status.sh`: Gets status for a document
- `test_list.sh`: Lists documents (e.g., uploaded, processed_technical)
- `sample_sow.txt`: Sample SOW for testing
- `.last_document_id`: Auto-generated helper file with last uploaded document id

## Quick Start
```bash
cd testing
export SOW_API_URL="https://<api-id>.execute-api.<region>.amazonaws.com/dev"
./test_all.sh
```

## File-Based Upload (Recommended)
```bash
# Upload the sample SOW
./test_upload.sh sample_sow.txt

# Upload with explicit customer name
./test_upload.sh sample_sow.txt "TechCorp Solutions Inc."

# Upload any local SOW document
./test_upload.sh /path/to/your/sow.txt "Your Customer Name"
```

After a successful upload, the document id is stored in `.last_document_id`.

## Individual Tests
```bash
# Process with an agent (use id from last upload)
./test_process.sh $(cat .last_document_id) technical
./test_process.sh $(cat .last_document_id) financial
./test_process.sh $(cat .last_document_id) legal

# Check status
./test_status.sh $(cat .last_document_id)

# List documents
./test_list.sh uploaded
```

## Notes
- `test_upload.sh` attempts to extract the customer name from the file content if not provided.
- Ensure IAM and model access are configured in AWS if you see AccessDenied errors.
- If you encounter endpoint timeouts, check CloudWatch logs for Lambda and API Gateway.

# New 2-lambda architecture outputs
output "agent_operations_function_name" {
  description = "Name of the agent operations Lambda function"
  value       = aws_lambda_function.agent_operations_handler.function_name
}

output "agent_operations_function_arn" {
  description = "ARN of the agent operations Lambda function"
  value       = aws_lambda_function.agent_operations_handler.arn
}

output "agent_operations_invoke_arn" {
  description = "Invoke ARN of the agent operations Lambda function"
  value       = aws_lambda_function.agent_operations_handler.invoke_arn
}

output "general_operations_function_name" {
  description = "Name of the general operations Lambda function"
  value       = aws_lambda_function.general_operations_handler.function_name
}

output "general_operations_function_arn" {
  description = "ARN of the general operations Lambda function"
  value       = aws_lambda_function.general_operations_handler.arn
}

output "general_operations_invoke_arn" {
  description = "Invoke ARN of the general operations Lambda function"
  value       = aws_lambda_function.general_operations_handler.invoke_arn
}

output "lambda_role_arn" {
  description = "ARN of the Lambda IAM role"
  value       = aws_iam_role.lambda_role.arn
}

# Backward compatibility outputs
output "upload_handler_function_name" {
  description = "Name of the upload handler <PERSON><PERSON> function (backward compatibility)"
  value       = aws_lambda_function.general_operations_handler.function_name
}

output "upload_handler_invoke_arn" {
  description = "Invoke ARN of the upload handler Lambda function (backward compatibility)"
  value       = aws_lambda_function.general_operations_handler.invoke_arn
}

output "agent_handler_function_name" {
  description = "Name of the agent handler Lambda function (backward compatibility)"
  value       = aws_lambda_function.agent_operations_handler.function_name
}

output "agent_handler_invoke_arn" {
  description = "Invoke ARN of the agent handler Lambda function (backward compatibility)"
  value       = aws_lambda_function.agent_operations_handler.invoke_arn
}

output "status_handler_function_name" {
  description = "Name of the status handler Lambda function (backward compatibility)"
  value       = aws_lambda_function.general_operations_handler.function_name
}

output "status_handler_invoke_arn" {
  description = "Invoke ARN of the status handler Lambda function (backward compatibility)"
  value       = aws_lambda_function.general_operations_handler.invoke_arn
}

output "list_handler_function_name" {
  description = "Name of the list handler Lambda function (backward compatibility)"
  value       = aws_lambda_function.general_operations_handler.function_name
}

output "list_handler_invoke_arn" {
  description = "Invoke ARN of the list handler Lambda function (backward compatibility)"
  value       = aws_lambda_function.general_operations_handler.invoke_arn
}

output "slack_scraper_function_name" {
  description = "Name of the Slack scraper Lambda function"
  value       = aws_lambda_function.slack_scraper.function_name
}

output "slack_scraper_function_arn" {
  description = "ARN of the Slack scraper Lambda function"
  value       = aws_lambda_function.slack_scraper.arn
}

output "slack_scraper_invoke_arn" {
  description = "Invoke ARN of the Slack scraper Lambda function"
  value       = aws_lambda_function.slack_scraper.invoke_arn
}

output "slack_channel_reviewer_function_name" {
  description = "Name of the Slack channel reviewer Lambda function"
  value       = aws_lambda_function.slack_channel_reviewer.function_name
}

output "slack_channel_reviewer_function_arn" {
  description = "ARN of the Slack channel reviewer Lambda function"
  value       = aws_lambda_function.slack_channel_reviewer.arn
}

output "slack_channel_reviewer_invoke_arn" {
  description = "Invoke ARN of the Slack channel reviewer Lambda function"
  value       = aws_lambda_function.slack_channel_reviewer.invoke_arn
}

# CloudWatch Log Group outputs
output "agent_operations_log_group_name" {
  description = "Name of the CloudWatch Log Group for agent operations Lambda"
  value       = aws_cloudwatch_log_group.agent_operations_logs.name
}

output "agent_operations_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for agent operations Lambda"
  value       = aws_cloudwatch_log_group.agent_operations_logs.arn
}

output "general_operations_log_group_name" {
  description = "Name of the CloudWatch Log Group for general operations Lambda"
  value       = aws_cloudwatch_log_group.general_operations_logs.name
}

output "general_operations_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for general operations Lambda"
  value       = aws_cloudwatch_log_group.general_operations_logs.arn
}

output "slack_scraper_log_group_name" {
  description = "Name of the CloudWatch Log Group for Slack scraper Lambda"
  value       = aws_cloudwatch_log_group.slack_scraper_logs.name
}

output "slack_scraper_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for Slack scraper Lambda"
  value       = aws_cloudwatch_log_group.slack_scraper_logs.arn
}

output "slack_channel_reviewer_log_group_name" {
  description = "Name of the CloudWatch Log Group for Slack channel reviewer Lambda"
  value       = aws_cloudwatch_log_group.slack_channel_reviewer_logs.name
}

output "slack_channel_reviewer_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for Slack channel reviewer Lambda"
  value       = aws_cloudwatch_log_group.slack_channel_reviewer_logs.arn
}

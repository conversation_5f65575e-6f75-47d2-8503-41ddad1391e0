# S3 Outputs
output "documents_bucket_name" {
  description = "Name of the S3 bucket for storing documents"
  value       = module.s3.documents_bucket_name
}

output "reviews_bucket_name" {
  description = "Name of the S3 bucket for storing reviews"
  value       = module.s3.reviews_bucket_name
}

# DynamoDB Outputs
output "document_status_table_name" {
  description = "Name of the document status DynamoDB table"
  value       = module.dynamodb.document_status_table_name
}

output "processing_results_table_name" {
  description = "Name of the processing results DynamoDB table"
  value       = module.dynamodb.processing_results_table_name
}

# Bedrock Agents Outputs
output "technical_agent_id" {
  description = "ID of the technical SOW reviewer agent"
  value       = module.bedrock_agents.technical_agent_id
}

output "technical_agent_alias_id" {
  description = "ID of the technical SOW reviewer agent alias"
  value       = module.bedrock_agents.technical_agent_alias_id
}

output "financial_agent_id" {
  description = "ID of the financial SOW reviewer agent"
  value       = module.bedrock_agents.financial_agent_id
}

output "financial_agent_alias_id" {
  description = "ID of the financial SOW reviewer agent alias"
  value       = module.bedrock_agents.financial_agent_alias_id
}

output "legal_agent_id" {
  description = "ID of the legal SOW reviewer agent"
  value       = module.bedrock_agents.legal_agent_id
}

output "legal_agent_alias_id" {
  description = "ID of the legal SOW reviewer agent alias"
  value       = module.bedrock_agents.legal_agent_alias_id
}

# Lambda Functions Outputs
output "upload_handler_function_name" {
  description = "Name of the upload handler Lambda function"
  value       = module.lambda.upload_handler_function_name
}

output "agent_handler_function_name" {
  description = "Name of the agent handler Lambda function"
  value       = module.lambda.agent_handler_function_name
}

output "status_handler_function_name" {
  description = "Name of the status handler Lambda function"
  value       = module.lambda.status_handler_function_name
}

output "list_handler_function_name" {
  description = "Name of the list handler Lambda function"
  value       = module.lambda.list_handler_function_name
}

# API Gateway Outputs
output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = module.api_gateway.api_gateway_url
}

output "upload_endpoint" {
  description = "Upload endpoint URL"
  value       = module.api_gateway.upload_endpoint
}

output "process_endpoint" {
  description = "Process endpoint URL"
  value       = module.api_gateway.process_endpoint
}

output "status_endpoint" {
  description = "Status endpoint URL"
  value       = module.api_gateway.status_endpoint
}

output "documents_endpoint" {
  description = "Documents list endpoint URL"
  value       = module.api_gateway.documents_endpoint
}

# Environment Information
output "environment" {
  description = "Current environment"
  value       = var.environment
}

output "region" {
  description = "AWS region"
  value       = var.region
}

# Slack Channel Reviewer

The Slack Channel Reviewer is a Lambda function that analyzes Slack channel content using Claude Sonnet 4 to extract insights for SOW (Statement of Work) document reviews.

## Overview

This function reads all text files from a specified Slack channel folder in S3, combines them chronologically (oldest first), and uses Bedrock Claude Sonnet 4 to analyze the content. The analysis extracts good points, bad points, and recommendations that can be used as a reference for reviewing future SOW documents.

## Features

- **Chronological Processing**: Reads and combines Slack channel text files in chronological order
- **AI Analysis**: Uses Claude Sonnet 4 to analyze channel content
- **Structured Output**: Extracts good points, bad points, and actionable recommendations
- **DynamoDB Storage**: Stores analysis results with metadata for future reference
- **Error Handling**: Graceful handling of missing files and API failures

## Architecture

### Components

1. **Lambda Function**: `slack_channel_reviewer`
   - Reads S3 files from `slack/{workspace_id}/channels/{channel_id}_{channel_name}/` folder
   - Processes text files chronologically
   - Calls Bedrock Claude Sonnet 4 for analysis
   - Stores results in DynamoDB

2. **DynamoDB Table**: `slack_channel_reviews`
   - Stores analysis results with metadata
   - Partition key: `channel_id`
   - Sort key: `review_date`

3. **S3 Bucket**: Slack export bucket
   - Organized by channel: `slack/{workspace_id}/channels/{channel_id}_{channel_name}/date={date_prefix}/{timestamp}.txt`
   - Text files with Unix timestamps for chronological ordering

### IAM Permissions

The Lambda function has the following permissions:
- S3 read access for slack export folder (`slack/{workspace_id}/channels/`)
- Bedrock invoke access for Claude Sonnet 4
- DynamoDB write access for storing results

## Usage

### Invoking the Lambda Function

The function expects an event with the following structure:

```json
{
  "channel_id": "C1234567890",
  "channel_name": "project-discussions"
}
```

### Example AWS CLI Command

```bash
aws lambda invoke \
  --function-name "sow-reviewer-slack-channel-reviewer" \
  --payload '{"channel_id": "C1234567890", "channel_name": "project-discussions"}' \
  --cli-binary-format raw-in-base64-out \
  response.json
```

### Example PowerShell Script

```powershell
$testEvent = @{
    channel_id = "C1234567890"
    channel_name = "project-discussions"
} | ConvertTo-Json

aws lambda invoke `
  --function-name "sow-reviewer-slack-channel-reviewer" `
  --payload $testEvent `
  --cli-binary-format raw-in-base64-out `
  response.json
```

## Analysis Prompt

The function uses a comprehensive prompt that asks Claude to:

1. **Review the channel content thoroughly**
2. **Extract good points**: Positive aspects, successful strategies, good practices
3. **Identify bad points**: Issues, problems, challenges, areas of concern
4. **Provide recommendations**: Actionable recommendations for future SOW reviews
5. **Focus on project insights**: Communication patterns, decision-making, stakeholder management

## DynamoDB Schema

The analysis results are stored with the following structure:

```json
{
  "channel_id": "C1234567890",
  "review_date": "2024-01-15T10:30:00",
  "channel_name": "project-discussions",
  "file_count": 15,
  "total_content_length": 50000,
  "analysis_results": {
    "good_points": ["Clear communication", "Timely updates"],
    "bad_points": ["Scope creep", "Delayed responses"],
    "recommendations": ["Set clear milestones", "Improve response times"]
  },
  "bedrock_response": "Raw Claude response text",
  "processing_time": 45.2,
  "status": "success"
}
```

## Error Handling

The function includes comprehensive error handling:

- **Missing files**: Graceful handling when no text files are found
- **API failures**: Fallback responses when Bedrock calls fail
- **Parsing errors**: Robust parsing of Claude responses
- **Storage errors**: Detailed logging for DynamoDB issues

## Testing

Use the provided test scripts:

- **Bash**: `testing/test_slack_channel_reviewer.sh`
- **PowerShell**: `testing/test_slack_channel_reviewer.ps1`

Update the `CHANNEL_ID` and `CHANNEL_NAME` variables in the scripts before running.

## Deployment

The function is deployed as part of the main Terraform infrastructure. To deploy:

```bash
terraform plan
terraform apply
```

## Monitoring

Monitor the function through:
- CloudWatch Logs
- DynamoDB table metrics
- Lambda function metrics

## Future Enhancements

Potential improvements:
- Batch processing for multiple channels
- Scheduled reviews via CloudWatch Events
- Integration with SOW review workflow
- Enhanced analysis with custom prompts
- Export functionality for analysis results

#!/bin/bash

# SOW Reviewer - List Documents Test Script
# Tests document listing functionality

set -e

# Configuration
API_URL="${SOW_API_URL:-https://x2m7lxjuph.execute-api.us-east-1.amazonaws.com/dev}"

echo "=== SOW Reviewer List Documents Test ==="
echo "API URL: $API_URL"
echo ""

# Get parameters
STATUS_FILTER="$1"
CUSTOMER_FILTER="$2"
LIMIT="${3:-10}"

# Default status filter
if [ -z "$STATUS_FILTER" ]; then
    STATUS_FILTER="uploaded"
fi

echo "Filters:"
echo "  Status: $STATUS_FILTER"
echo "  Customer: $CUSTOMER_FILTER"
echo "  Limit: $LIMIT"
echo ""

# Build query parameters
QUERY_PARAMS="status=$STATUS_FILTER&limit=$LIMIT"

if [ -n "$CUSTOMER_FILTER" ]; then
    QUERY_PARAMS="$QUERY_PARAMS&customer_name=$CUSTOMER_FILTER"
fi

echo "Listing documents..."
echo ""

# List documents
RESPONSE=$(curl -s -X GET "$API_URL/documents?$QUERY_PARAMS")

echo "Response:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

# Parse response
# COUNT=$(echo "$RESPONSE" | jq -r '.count' 2>/dev/null || echo "")
# DOCUMENTS=$(echo "$RESPONSE" | jq -r '.documents' 2>/dev/null || echo "")

# if [ "$COUNT" != "null" ] && [ -n "$COUNT" ]; then
#     echo ""
#     echo "✅ List request successful!"
#     echo "Found $COUNT documents"
    
#     if [ "$COUNT" -gt 0 ]; then
#         echo ""
#         echo "Documents:"
        
#         # Display document details
#         echo "$RESPONSE" | jq -r '.documents[] | "  📄 \(.file_name) (\(.document_id))
#     Customer: \(.customer_name // "Unknown")
#     Status: \(.status)
#     Created: \(.created_at)
#     S3 URI: \(.s3_uri)
# "' 2>/dev/null || {
#             echo "  Document list available in response above"
#         }
        
#         # Show example commands for first document
#         FIRST_DOC_ID=$(echo "$RESPONSE" | jq -r '.documents[0].document_id' 2>/dev/null || echo "")
#         if [ "$FIRST_DOC_ID" != "null" ] && [ -n "$FIRST_DOC_ID" ]; then
#             echo ""
#             echo "Example commands for first document:"
#             echo "  ./test_status.sh $FIRST_DOC_ID"
#             echo "  ./test_process.sh $FIRST_DOC_ID technical"
#         fi
#     else
#         echo ""
#         echo "No documents found with the specified filters."
#         echo ""
#         echo "Try different filters:"
#         echo "  ./test_list.sh uploaded           # List uploaded documents"
#         echo "  ./test_list.sh processed          # List processed documents"
#         echo "  ./test_list.sh uploaded Acme_Corp # List by customer"
#         echo ""
#         echo "Or upload a new document:"
#         echo "  ./test_upload.sh"
#     fi
# else
#     echo ""
#     echo "❌ List request failed!"
#     echo "Check the response above for error details"
#     exit 1
# fi

echo ""
echo "Usage examples:"
echo "  $0                          # List uploaded documents (default)"
echo "  $0 uploaded                 # List uploaded documents"
echo "  $0 processed                # List processed documents" 
echo "  $0 uploaded Acme_Corp       # List by status and customer"
echo "  $0 uploaded \"\" 20           # List with custom limit"

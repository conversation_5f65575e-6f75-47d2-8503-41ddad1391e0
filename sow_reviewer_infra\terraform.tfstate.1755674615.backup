{"version": 4, "terraform_version": "1.9.4", "serial": 140, "lineage": "5757cae7-4434-1582-a329-ccd618a82c78", "outputs": {"agent_handler_function_name": {"value": "dev-sow-agent-operations", "type": "string"}, "api_gateway_url": {"value": "https://rga7e7kz97.execute-api.us-east-1.amazonaws.com/dev", "type": "string"}, "document_status_table_name": {"value": "dev-sow-document-status", "type": "string"}, "documents_bucket_name": {"value": "**********85-us-east-1-dev-sow-main-bucket", "type": "string"}, "documents_endpoint": {"value": "https://rga7e7kz97.execute-api.us-east-1.amazonaws.com/dev/documents", "type": "string"}, "environment": {"value": "dev", "type": "string"}, "financial_agent_alias_id": {"value": "5OI8QB7W9W", "type": "string"}, "financial_agent_id": {"value": "NOINFUVFCR", "type": "string"}, "legal_agent_alias_id": {"value": "B4RBKTCP1H", "type": "string"}, "legal_agent_id": {"value": "D69XRDXGHY", "type": "string"}, "list_handler_function_name": {"value": "dev-sow-general-operations", "type": "string"}, "process_endpoint": {"value": "https://rga7e7kz97.execute-api.us-east-1.amazonaws.com/dev/process", "type": "string"}, "processing_results_table_name": {"value": "dev-sow-processing-results", "type": "string"}, "region": {"value": "us-east-1", "type": "string"}, "reviews_bucket_name": {"value": "**********85-us-east-1-dev-sow-main-bucket", "type": "string"}, "status_endpoint": {"value": "https://rga7e7kz97.execute-api.us-east-1.amazonaws.com/dev/status", "type": "string"}, "status_handler_function_name": {"value": "dev-sow-general-operations", "type": "string"}, "technical_agent_alias_id": {"value": "GCU6KO3G1T", "type": "string"}, "technical_agent_id": {"value": "IVOHTPUVHS", "type": "string"}, "upload_endpoint": {"value": "https://rga7e7kz97.execute-api.us-east-1.amazonaws.com/dev/upload", "type": "string"}, "upload_handler_function_name": {"value": "dev-sow-general-operations", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "**********85", "arn": "arn:aws:iam::**********85:user/nour", "id": "**********85", "user_id": "AIDAU6GDUSF4TSNFVXDKJ"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "US East (N. Virginia)", "endpoint": "ec2.us-east-1.amazonaws.com", "id": "us-east-1", "name": "us-east-1", "region": "us-east-1"}, "sensitive_attributes": []}]}, {"module": "module.api_gateway", "mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "US East (N. Virginia)", "endpoint": "ec2.us-east-1.amazonaws.com", "id": "us-east-1", "name": "us-east-1", "region": "us-east-1"}, "sensitive_attributes": []}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_deployment", "name": "sow_api_deployment", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"created_date": "2025-08-18T21:59:34Z", "description": "", "id": "i5ikka", "region": "us-east-1", "rest_api_id": "rga7e7kz97", "triggers": {"redeployment": "1b7eacc0af6d82fa88280f9a3c01249fd6ef80b5"}, "variables": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.api_gateway.aws_api_gateway_integration.documents_integration", "module.api_gateway.aws_api_gateway_integration.process_integration", "module.api_gateway.aws_api_gateway_integration.status_integration", "module.api_gateway.aws_api_gateway_integration.upload_integration", "module.api_gateway.aws_api_gateway_method.documents_get", "module.api_gateway.aws_api_gateway_method.process_post", "module.api_gateway.aws_api_gateway_method.status_get", "module.api_gateway.aws_api_gateway_method.upload_post", "module.api_gateway.aws_api_gateway_resource.documents", "module.api_gateway.aws_api_gateway_resource.process", "module.api_gateway.aws_api_gateway_resource.status", "module.api_gateway.aws_api_gateway_resource.status_document_id", "module.api_gateway.aws_api_gateway_resource.upload", "module.api_gateway.aws_api_gateway_rest_api.sow_api", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.aws_lambda_function.agent_operations_handler", "module.lambda.aws_lambda_function.general_operations_handler", "module.lambda.data.archive_file.agent_operations_handler", "module.lambda.data.archive_file.general_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "cors_documents", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "jm20b7", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "OPTIONS", "id": "agi-rga7e7kz97-jm20b7-OPTIONS", "integration_http_method": "", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": {}, "request_templates": {"application/json": "{\"statusCode\":200}"}, "resource_id": "jm20b7", "rest_api_id": "rga7e7kz97", "timeout_milliseconds": 29000, "tls_config": [], "type": "MOCK", "uri": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_documents", "module.api_gateway.aws_api_gateway_resource.documents", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "cors_process", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "h36r9p", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "OPTIONS", "id": "agi-rga7e7kz97-h36r9p-OPTIONS", "integration_http_method": "", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": {}, "request_templates": {"application/json": "{\"statusCode\":200}"}, "resource_id": "h36r9p", "rest_api_id": "rga7e7kz97", "timeout_milliseconds": 29000, "tls_config": [], "type": "MOCK", "uri": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_process", "module.api_gateway.aws_api_gateway_resource.process", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "cors_upload", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "9ztvee", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "OPTIONS", "id": "agi-rga7e7kz97-9ztvee-OPTIONS", "integration_http_method": "", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": {}, "request_templates": {"application/json": "{\"statusCode\":200}"}, "resource_id": "9ztvee", "rest_api_id": "rga7e7kz97", "timeout_milliseconds": 29000, "tls_config": [], "type": "MOCK", "uri": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_upload", "module.api_gateway.aws_api_gateway_resource.upload", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "documents_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "jm20b7", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "GET", "id": "agi-rga7e7kz97-jm20b7-GET", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": {"integration.request.querystring.limit": "method.request.querystring.limit", "integration.request.querystring.status": "method.request.querystring.status"}, "request_templates": {}, "resource_id": "jm20b7", "rest_api_id": "rga7e7kz97", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:**********85:function:dev-sow-general-operations/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.api_gateway.aws_api_gateway_method.documents_get", "module.api_gateway.aws_api_gateway_resource.documents", "module.api_gateway.aws_api_gateway_rest_api.sow_api", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.aws_lambda_function.general_operations_handler", "module.lambda.data.archive_file.general_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "process_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "h36r9p", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-rga7e7kz97-h36r9p-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": {}, "request_templates": {}, "resource_id": "h36r9p", "rest_api_id": "rga7e7kz97", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:**********85:function:dev-sow-agent-operations/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.api_gateway.aws_api_gateway_method.process_post", "module.api_gateway.aws_api_gateway_resource.process", "module.api_gateway.aws_api_gateway_rest_api.sow_api", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.aws_lambda_function.agent_operations_handler", "module.lambda.data.archive_file.agent_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "status_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "rx187g", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "GET", "id": "agi-rga7e7kz97-rx187g-GET", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": {"integration.request.path.document_id": "method.request.path.document_id"}, "request_templates": {}, "resource_id": "rx187g", "rest_api_id": "rga7e7kz97", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:**********85:function:dev-sow-general-operations/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.api_gateway.aws_api_gateway_method.status_get", "module.api_gateway.aws_api_gateway_resource.status", "module.api_gateway.aws_api_gateway_resource.status_document_id", "module.api_gateway.aws_api_gateway_rest_api.sow_api", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.aws_lambda_function.general_operations_handler", "module.lambda.data.archive_file.general_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "upload_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "9ztvee", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-rga7e7kz97-9ztvee-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "region": "us-east-1", "request_parameters": {}, "request_templates": {}, "resource_id": "9ztvee", "rest_api_id": "rga7e7kz97", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:**********85:function:dev-sow-general-operations/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.api_gateway.aws_api_gateway_method.upload_post", "module.api_gateway.aws_api_gateway_resource.upload", "module.api_gateway.aws_api_gateway_rest_api.sow_api", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.aws_lambda_function.general_operations_handler", "module.lambda.data.archive_file.general_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "cors_documents", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "OPTIONS", "id": "agir-rga7e7kz97-jm20b7-OPTIONS-200", "region": "us-east-1", "resource_id": "jm20b7", "response_parameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'", "method.response.header.Access-Control-Allow-Methods": "'GET,OPTIONS,POST'", "method.response.header.Access-Control-Allow-Origin": "'*'"}, "response_templates": {}, "rest_api_id": "rga7e7kz97", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_documents", "module.api_gateway.aws_api_gateway_method_response.cors_documents", "module.api_gateway.aws_api_gateway_resource.documents", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "cors_process", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "OPTIONS", "id": "agir-rga7e7kz97-h36r9p-OPTIONS-200", "region": "us-east-1", "resource_id": "h36r9p", "response_parameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'", "method.response.header.Access-Control-Allow-Methods": "'GET,OPTIONS,POST'", "method.response.header.Access-Control-Allow-Origin": "'*'"}, "response_templates": {}, "rest_api_id": "rga7e7kz97", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_process", "module.api_gateway.aws_api_gateway_method_response.cors_process", "module.api_gateway.aws_api_gateway_resource.process", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "cors_upload", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "OPTIONS", "id": "agir-rga7e7kz97-9ztvee-OPTIONS-200", "region": "us-east-1", "resource_id": "9ztvee", "response_parameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'", "method.response.header.Access-Control-Allow-Methods": "'GET,OPTIONS,POST'", "method.response.header.Access-Control-Allow-Origin": "'*'"}, "response_templates": {}, "rest_api_id": "rga7e7kz97", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_upload", "module.api_gateway.aws_api_gateway_method_response.cors_upload", "module.api_gateway.aws_api_gateway_resource.upload", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "cors_documents", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "OPTIONS", "id": "agm-rga7e7kz97-jm20b7-OPTIONS", "operation_name": "", "region": "us-east-1", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "jm20b7", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.documents", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "cors_process", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "OPTIONS", "id": "agm-rga7e7kz97-h36r9p-OPTIONS", "operation_name": "", "region": "us-east-1", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "h36r9p", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.process", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "cors_upload", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "OPTIONS", "id": "agm-rga7e7kz97-9ztvee-OPTIONS", "operation_name": "", "region": "us-east-1", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "9ztvee", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.upload", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "documents_get", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "GET", "id": "agm-rga7e7kz97-jm20b7-GET", "operation_name": "", "region": "us-east-1", "request_models": {}, "request_parameters": {"method.request.querystring.limit": false, "method.request.querystring.status": false}, "request_validator_id": "", "resource_id": "jm20b7", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.documents", "module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "process_post", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-rga7e7kz97-h36r9p-POST", "operation_name": "", "region": "us-east-1", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "h36r9p", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.process", "module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "status_get", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "GET", "id": "agm-rga7e7kz97-rx187g-GET", "operation_name": "", "region": "us-east-1", "request_models": {}, "request_parameters": {"method.request.path.document_id": true}, "request_validator_id": "", "resource_id": "rx187g", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.status", "module.api_gateway.aws_api_gateway_resource.status_document_id", "module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "upload_post", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-rga7e7kz97-9ztvee-POST", "operation_name": "", "region": "us-east-1", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "9ztvee", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.upload", "module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "cors_documents", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "OPTIONS", "id": "agmr-rga7e7kz97-jm20b7-OPTIONS-200", "region": "us-east-1", "resource_id": "jm20b7", "response_models": {}, "response_parameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}, "rest_api_id": "rga7e7kz97", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_documents", "module.api_gateway.aws_api_gateway_resource.documents", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "cors_process", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "OPTIONS", "id": "agmr-rga7e7kz97-h36r9p-OPTIONS-200", "region": "us-east-1", "resource_id": "h36r9p", "response_models": {}, "response_parameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}, "rest_api_id": "rga7e7kz97", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_process", "module.api_gateway.aws_api_gateway_resource.process", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "cors_upload", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "OPTIONS", "id": "agmr-rga7e7kz97-9ztvee-OPTIONS-200", "region": "us-east-1", "resource_id": "9ztvee", "response_models": {}, "response_parameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}, "rest_api_id": "rga7e7kz97", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_upload", "module.api_gateway.aws_api_gateway_resource.upload", "module.api_gateway.aws_api_gateway_rest_api.sow_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "documents", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "jm20b7", "parent_id": "tqve2kep3a", "path": "/documents", "path_part": "documents", "region": "us-east-1", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "process", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "h36r9p", "parent_id": "tqve2kep3a", "path": "/process", "path_part": "process", "region": "us-east-1", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "status", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "qr16af", "parent_id": "tqve2kep3a", "path": "/status", "path_part": "status", "region": "us-east-1", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "status_document_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "rx187g", "parent_id": "qr16af", "path": "/status/{document_id}", "path_part": "{document_id}", "region": "us-east-1", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.status", "module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "upload", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "9ztvee", "parent_id": "tqve2kep3a", "path": "/upload", "path_part": "upload", "region": "us-east-1", "rest_api_id": "rga7e7kz97"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.sow_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_rest_api", "name": "sow_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_source": "HEADER", "arn": "arn:aws:apigateway:us-east-1::/restapis/rga7e7kz97", "binary_media_types": [], "body": null, "created_date": "2025-08-18T21:58:42Z", "description": "SOW Reviewer API for dev environment", "disable_execute_api_endpoint": false, "endpoint_configuration": [{"ip_address_type": "ipv4", "types": ["EDGE"], "vpc_endpoint_ids": []}], "execution_arn": "arn:aws:execute-api:us-east-1:**********85:rga7e7kz97", "fail_on_warnings": null, "id": "rga7e7kz97", "minimum_compression_size": "", "name": "dev-sow-reviewer-api", "parameters": null, "policy": "", "put_rest_api_mode": null, "region": "us-east-1", "root_resource_id": "tqve2kep3a", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_stage", "name": "sow_api_stage", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_log_settings": [], "arn": "arn:aws:apigateway:us-east-1::/restapis/rga7e7kz97/stages/dev", "cache_cluster_enabled": false, "cache_cluster_size": "", "canary_settings": [], "client_certificate_id": "", "deployment_id": "i5ikka", "description": "", "documentation_version": "", "execution_arn": "arn:aws:execute-api:us-east-1:**********85:rga7e7kz97/dev", "id": "ags-rga7e7kz97-dev", "invoke_url": "https://rga7e7kz97.execute-api.us-east-1.amazonaws.com/dev", "region": "us-east-1", "rest_api_id": "rga7e7kz97", "stage_name": "dev", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "variables": {}, "web_acl_arn": "", "xray_tracing_enabled": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.api_gateway.aws_api_gateway_deployment.sow_api_deployment", "module.api_gateway.aws_api_gateway_integration.documents_integration", "module.api_gateway.aws_api_gateway_integration.process_integration", "module.api_gateway.aws_api_gateway_integration.status_integration", "module.api_gateway.aws_api_gateway_integration.upload_integration", "module.api_gateway.aws_api_gateway_method.documents_get", "module.api_gateway.aws_api_gateway_method.process_post", "module.api_gateway.aws_api_gateway_method.status_get", "module.api_gateway.aws_api_gateway_method.upload_post", "module.api_gateway.aws_api_gateway_resource.documents", "module.api_gateway.aws_api_gateway_resource.process", "module.api_gateway.aws_api_gateway_resource.status", "module.api_gateway.aws_api_gateway_resource.status_document_id", "module.api_gateway.aws_api_gateway_resource.upload", "module.api_gateway.aws_api_gateway_rest_api.sow_api", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.aws_lambda_function.agent_operations_handler", "module.lambda.aws_lambda_function.general_operations_handler", "module.lambda.data.archive_file.agent_operations_handler", "module.lambda.data.archive_file.general_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_lambda_permission", "name": "process_permission", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "dev-sow-agent-operations", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "region": "us-east-1", "source_account": null, "source_arn": "arn:aws:execute-api:us-east-1:**********85:rga7e7kz97/*/*", "statement_id": "AllowExecutionFromAPIGateway", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.api_gateway.aws_api_gateway_rest_api.sow_api", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.aws_lambda_function.agent_operations_handler", "module.lambda.data.archive_file.agent_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_lambda_permission", "name": "upload_permission", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "dev-sow-general-operations", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "region": "us-east-1", "source_account": null, "source_arn": "arn:aws:execute-api:us-east-1:**********85:rga7e7kz97/*/*", "statement_id": "AllowExecutionFromAPIGateway", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.api_gateway.aws_api_gateway_rest_api.sow_api", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.aws_lambda_function.general_operations_handler", "module.lambda.data.archive_file.general_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"]}]}, {"module": "module.bedrock_agents", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "**********85", "arn": "arn:aws:iam::**********85:user/nour", "id": "**********85", "user_id": "AIDAU6GDUSF4TSNFVXDKJ"}, "sensitive_attributes": []}]}, {"module": "module.bedrock_agents", "mode": "data", "type": "aws_iam_policy_document", "name": "bedrock_agent_permissions", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"bedrock:UseInferenceProfile\",\n        \"bedrock:InvokeModelWithResponseStream\",\n        \"bedrock:InvokeModel\",\n        \"bedrock:GetInferenceProfile\",\n        \"bedrock:GetFoundationModel\"\n      ],\n      \"Resource\": [\n        \"arn:aws:bedrock:us-east-1:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\",\n        \"arn:aws:bedrock:us-east-1:**********85:foundation-model/anthropic.claude-sonnet-4-********-v1:0\",\n        \"arn:aws:bedrock:*::foundation-model/anthropic.claude-sonnet-4-********-v1:0\",\n        \"arn:aws:bedrock:*:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"bedrock:UseInferenceProfile\",\n      \"Resource\": \"arn:aws:bedrock:us-east-1:**********85:inference-profile/*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"bedrock:UseInferenceProfile\",\"bedrock:InvokeModelWithResponseStream\",\"bedrock:InvokeModel\",\"bedrock:GetInferenceProfile\",\"bedrock:GetFoundationModel\"],\"Resource\":[\"arn:aws:bedrock:us-east-1:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:us-east-1:**********85:foundation-model/anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:*::foundation-model/anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:*:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\"]},{\"Effect\":\"Allow\",\"Action\":\"bedrock:UseInferenceProfile\",\"Resource\":\"arn:aws:bedrock:us-east-1:**********85:inference-profile/*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["bedrock:GetFoundationModel", "bedrock:GetInferenceProfile", "bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream", "bedrock:UseInferenceProfile"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:bedrock:*:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0", "arn:aws:bedrock:*::foundation-model/anthropic.claude-sonnet-4-********-v1:0", "arn:aws:bedrock:us-east-1:**********85:foundation-model/anthropic.claude-sonnet-4-********-v1:0", "arn:aws:bedrock:us-east-1:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"], "sid": ""}, {"actions": ["bedrock:UseInferenceProfile"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:bedrock:us-east-1:**********85:inference-profile/*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.bedrock_agents", "mode": "data", "type": "aws_iam_policy_document", "name": "bedrock_agent_trust_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"AmazonBedrockAgentInferenceProfilesCrossRegionPolicyProd\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"bedrock.amazonaws.com\"\n      },\n      \"Condition\": {\n        \"ArnLike\": {\n          \"aws:SourceArn\": \"arn:aws:bedrock:us-east-1:**********85:agent/*\"\n        },\n        \"StringEquals\": {\n          \"aws:SourceAccount\": \"**********85\"\n        }\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"AmazonBedrockAgentInferenceProfilesCrossRegionPolicyProd\",\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"bedrock.amazonaws.com\"},\"Condition\":{\"ArnLike\":{\"aws:SourceArn\":\"arn:aws:bedrock:us-east-1:**********85:agent/*\"},\"StringEquals\":{\"aws:SourceAccount\":\"**********85\"}}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [{"test": "ArnLike", "values": ["arn:aws:bedrock:us-east-1:**********85:agent/*"], "variable": "aws:SourceArn"}, {"test": "StringEquals", "values": ["**********85"], "variable": "aws:SourceAccount"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["bedrock.amazonaws.com"], "type": "Service"}], "resources": [], "sid": "AmazonBedrockAgentInferenceProfilesCrossRegionPolicyProd"}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.bedrock_agents", "mode": "managed", "type": "aws_bedrockagent_agent", "name": "financial_agent", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"agent_arn": "arn:aws:bedrock:us-east-1:**********85:agent/NOINFUVFCR", "agent_collaboration": "DISABLED", "agent_id": "NOINFUVFCR", "agent_name": "dev-financial-sow-reviewer-agent", "agent_resource_role_arn": "arn:aws:iam::**********85:role/BedrockAgents-dev-20250818215842200300000002", "agent_version": null, "customer_encryption_key_arn": null, "description": null, "foundation_model": "arn:aws:bedrock:us-east-1:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0", "guardrail_configuration": null, "id": "NOINFUVFCR", "idle_session_ttl_in_seconds": 500, "instruction": "You are a project manager for a Cloud Consultation company. You are tasked with reviewing Statement of Work documents from a \r\nfinancial perspective. Identify if all the correct financial verbiage is present. If there are any AWS funding programs mentioned, \r\nensure the Statement of Work accurately reflects any prerequisites to get them, such as MAP requiring we have specific deliverables \r\nlike the Migration Readiness Assessment. Ensure your responses are as accurate as possible and avoid hallucinations. \r\nThe first line of your response should be \"Financial Review of Statement of Work For\" and then the customer name. However, \r\nin the rest of the document, the customer should be referred to as 'The Customer.'\r\n", "memory_configuration": null, "prepare_agent": true, "prepared_at": "2025-08-20T07:13:24Z", "prompt_override_configuration": [{"override_lambda": null, "prompt_configurations": null}], "region": "us-east-1", "skip_resource_in_use_check": false, "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeouts": null}, "sensitive_attributes": [], "dependencies": ["data.aws_caller_identity.current", "module.bedrock_agents.aws_iam_role.bedrock_agent_role", "module.bedrock_agents.aws_iam_role_policy.bedrock_agent_policy", "module.bedrock_agents.data.aws_caller_identity.current", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_permissions", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_trust_policy"]}]}, {"module": "module.bedrock_agents", "mode": "managed", "type": "aws_bedrockagent_agent", "name": "legal_agent", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"agent_arn": "arn:aws:bedrock:us-east-1:**********85:agent/D69XRDXGHY", "agent_collaboration": "DISABLED", "agent_id": "D69XRDXGHY", "agent_name": "dev-legal-sow-reviewer-agent", "agent_resource_role_arn": "arn:aws:iam::**********85:role/BedrockAgents-dev-20250818215842200300000002", "agent_version": null, "customer_encryption_key_arn": null, "description": null, "foundation_model": "arn:aws:bedrock:us-east-1:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0", "guardrail_configuration": null, "id": "D69XRDXGHY", "idle_session_ttl_in_seconds": 500, "instruction": "You are a project manager for a Cloud Consultation company. You are tasked with reviewing Statement of Work documents from a \r\nlegal perspective. Identify whether we are using any language that a dishonest customer could use to hold us hostage, get free work \r\nor otherwise cause us problems if they really wanted to nitpick. Identify whether we are making any promises with our verbiage \r\nthat imply we are making promises beyond the scope of the SOW.  Ensure your responses are as accurate as possible and avoid hallucinations. \r\nThe first line of your response should be \"Legal Review of Statement of Work For\" and then the customer name. \r\nHowever, in the rest of the document, the customer should be referred to as 'The Customer.'\r\n", "memory_configuration": null, "prepare_agent": true, "prepared_at": "2025-08-20T07:13:24Z", "prompt_override_configuration": [{"override_lambda": null, "prompt_configurations": null}], "region": "us-east-1", "skip_resource_in_use_check": false, "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeouts": null}, "sensitive_attributes": [], "dependencies": ["data.aws_caller_identity.current", "module.bedrock_agents.aws_iam_role.bedrock_agent_role", "module.bedrock_agents.aws_iam_role_policy.bedrock_agent_policy", "module.bedrock_agents.data.aws_caller_identity.current", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_permissions", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_trust_policy"]}]}, {"module": "module.bedrock_agents", "mode": "managed", "type": "aws_bedrockagent_agent", "name": "technical_agent", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"agent_arn": "arn:aws:bedrock:us-east-1:**********85:agent/IVOHTPUVHS", "agent_collaboration": "DISABLED", "agent_id": "IVOHTPUVHS", "agent_name": "dev-technical-sow-reviewer-agent", "agent_resource_role_arn": "arn:aws:iam::**********85:role/BedrockAgents-dev-20250818215842200300000002", "agent_version": null, "customer_encryption_key_arn": null, "description": null, "foundation_model": "arn:aws:bedrock:us-east-1:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0", "guardrail_configuration": null, "id": "IVOHTPUVHS", "idle_session_ttl_in_seconds": 500, "instruction": "You are a project manager for a Cloud Consultation company. You are tasked with reviewing Statement of Work documents from a technical perspective.\r\nIdentify whether there are any portions which may not be feasible, such as overpromising the accuracy of an AI model or on an SLA.\r\nIdentify whether there is any missing language, such as limits or the amount of resources that must be used to meet a certain goal, or any mention of migration without mention of the services needed to setup the new environment.\r\nEnsure your responses are as accurate as possible and avoid hallucinations.\r\nThe first line of your response should be \"Technical Review of Statement of Work For\" and then the customer name.\r\nHowever, in the rest of the document, the customer should be referred to as 'The Customer.'\r\n", "memory_configuration": null, "prepare_agent": true, "prepared_at": "2025-08-20T07:13:24Z", "prompt_override_configuration": [{"override_lambda": null, "prompt_configurations": null}], "region": "us-east-1", "skip_resource_in_use_check": false, "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeouts": null}, "sensitive_attributes": [], "dependencies": ["data.aws_caller_identity.current", "module.bedrock_agents.aws_iam_role.bedrock_agent_role", "module.bedrock_agents.aws_iam_role_policy.bedrock_agent_policy", "module.bedrock_agents.data.aws_caller_identity.current", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_permissions", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_trust_policy"]}]}, {"module": "module.bedrock_agents", "mode": "managed", "type": "aws_bedrockagent_agent_alias", "name": "financial_agent_alias", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"agent_alias_arn": "arn:aws:bedrock:us-east-1:**********85:agent-alias/NOINFUVFCR/5OI8QB7W9W", "agent_alias_id": "5OI8QB7W9W", "agent_alias_name": "dev-financial-agent-alias", "agent_id": "NOINFUVFCR", "description": "Financial Agent <PERSON><PERSON> for dev", "id": "5OI8QB7W9W,NOINFUVFCR", "region": "us-east-1", "routing_configuration": [{"agent_version": "1", "provisioned_throughput": null}], "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeouts": null}, "sensitive_attributes": [], "dependencies": ["data.aws_caller_identity.current", "module.bedrock_agents.aws_bedrockagent_agent.financial_agent", "module.bedrock_agents.aws_iam_role.bedrock_agent_role", "module.bedrock_agents.aws_iam_role_policy.bedrock_agent_policy", "module.bedrock_agents.data.aws_caller_identity.current", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_permissions", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_trust_policy"]}]}, {"module": "module.bedrock_agents", "mode": "managed", "type": "aws_bedrockagent_agent_alias", "name": "legal_agent_alias", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"agent_alias_arn": "arn:aws:bedrock:us-east-1:**********85:agent-alias/D69XRDXGHY/B4RBKTCP1H", "agent_alias_id": "B4RBKTCP1H", "agent_alias_name": "dev-legal-agent-alias", "agent_id": "D69XRDXGHY", "description": "Legal Agent <PERSON><PERSON> for dev", "id": "B4RBKTCP1H,D69XRDXGHY", "region": "us-east-1", "routing_configuration": [{"agent_version": "1", "provisioned_throughput": null}], "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeouts": null}, "sensitive_attributes": [], "dependencies": ["data.aws_caller_identity.current", "module.bedrock_agents.aws_bedrockagent_agent.legal_agent", "module.bedrock_agents.aws_iam_role.bedrock_agent_role", "module.bedrock_agents.aws_iam_role_policy.bedrock_agent_policy", "module.bedrock_agents.data.aws_caller_identity.current", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_permissions", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_trust_policy"]}]}, {"module": "module.bedrock_agents", "mode": "managed", "type": "aws_bedrockagent_agent_alias", "name": "technical_agent_alias", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"agent_alias_arn": "arn:aws:bedrock:us-east-1:**********85:agent-alias/IVOHTPUVHS/GCU6KO3G1T", "agent_alias_id": "GCU6KO3G1T", "agent_alias_name": "dev-technical-agent-alias", "agent_id": "IVOHTPUVHS", "description": "Technical Agent <PERSON><PERSON> for dev", "id": "GCU6KO3G1T,IVOHTPUVHS", "region": "us-east-1", "routing_configuration": [{"agent_version": "1", "provisioned_throughput": null}], "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeouts": null}, "sensitive_attributes": [], "dependencies": ["data.aws_caller_identity.current", "module.bedrock_agents.aws_bedrockagent_agent.technical_agent", "module.bedrock_agents.aws_iam_role.bedrock_agent_role", "module.bedrock_agents.aws_iam_role_policy.bedrock_agent_policy", "module.bedrock_agents.data.aws_caller_identity.current", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_permissions", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_trust_policy"]}]}, {"module": "module.bedrock_agents", "mode": "managed", "type": "aws_iam_role", "name": "bedrock_agent_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::**********85:role/BedrockAgents-dev-20250818215842200300000002", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Condition\":{\"ArnLike\":{\"aws:SourceArn\":\"arn:aws:bedrock:us-east-1:**********85:agent/*\"},\"StringEquals\":{\"aws:SourceAccount\":\"**********85\"}},\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"bedrock.amazonaws.com\"},\"Sid\":\"AmazonBedrockAgentInferenceProfilesCrossRegionPolicyProd\"}],\"Version\":\"2012-10-17\"}", "create_date": "2025-08-18T21:58:42Z", "description": "", "force_detach_policies": false, "id": "BedrockAgents-dev-20250818215842200300000002", "inline_policy": [{"name": "BedrockAgentPolicy_dev_20250818215843557600000003", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"bedrock:UseInferenceProfile\",\"bedrock:InvokeModelWithResponseStream\",\"bedrock:InvokeModel\",\"bedrock:GetInferenceProfile\",\"bedrock:GetFoundationModel\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:bedrock:us-east-1:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:us-east-1:**********85:foundation-model/anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:*::foundation-model/anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:*:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\"]},{\"Action\":\"bedrock:UseInferenceProfile\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:bedrock:us-east-1:**********85:inference-profile/*\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "BedrockAgents-dev-20250818215842200300000002", "name_prefix": "BedrockAgents-dev-", "path": "/", "permissions_boundary": "", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "unique_id": "AROAU6GDUSF4X43HNO4RN"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.bedrock_agents.data.aws_caller_identity.current", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_trust_policy"]}]}, {"module": "module.bedrock_agents", "mode": "managed", "type": "aws_iam_role_policy", "name": "bedrock_agent_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "BedrockAgents-dev-20250818215842200300000002:BedrockAgentPolicy_dev_20250818215843557600000003", "name": "BedrockAgentPolicy_dev_20250818215843557600000003", "name_prefix": "BedrockAgentPolicy_dev_", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"bedrock:UseInferenceProfile\",\"bedrock:InvokeModelWithResponseStream\",\"bedrock:InvokeModel\",\"bedrock:GetInferenceProfile\",\"bedrock:GetFoundationModel\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:bedrock:us-east-1:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:us-east-1:**********85:foundation-model/anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:*::foundation-model/anthropic.claude-sonnet-4-********-v1:0\",\"arn:aws:bedrock:*:**********85:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0\"]},{\"Action\":\"bedrock:UseInferenceProfile\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:bedrock:us-east-1:**********85:inference-profile/*\"}]}", "role": "BedrockAgents-dev-20250818215842200300000002"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.bedrock_agents.aws_iam_role.bedrock_agent_role", "module.bedrock_agents.data.aws_caller_identity.current", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_permissions", "module.bedrock_agents.data.aws_iam_policy_document.bedrock_agent_trust_policy"]}]}, {"module": "module.dynamodb", "mode": "managed", "type": "aws_dynamodb_table", "name": "document_status", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status", "attribute": [{"name": "created_at", "type": "S"}, {"name": "document_id", "type": "S"}, {"name": "status", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [{"hash_key": "status", "name": "StatusIndex", "non_key_attributes": [], "on_demand_throughput": [], "projection_type": "ALL", "range_key": "created_at", "read_capacity": 0, "write_capacity": 0}], "hash_key": "document_id", "id": "dev-sow-document-status", "import_table": [], "local_secondary_index": [], "name": "dev-sow-document-status", "on_demand_throughput": [], "point_in_time_recovery": [{"enabled": false, "recovery_period_in_days": 0}], "range_key": null, "read_capacity": 0, "region": "us-east-1", "replica": [], "restore_date_time": null, "restore_source_name": null, "restore_source_table_arn": null, "restore_to_latest_time": null, "server_side_encryption": [], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-sow-document-status", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-sow-document-status", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeouts": null, "ttl": [{"attribute_name": "", "enabled": false}], "write_capacity": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "create_before_destroy": true}]}, {"module": "module.dynamodb", "mode": "managed", "type": "aws_dynamodb_table", "name": "processing_results", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results", "attribute": [{"name": "agent_type", "type": "S"}, {"name": "document_id", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [], "hash_key": "document_id", "id": "dev-sow-processing-results", "import_table": [], "local_secondary_index": [], "name": "dev-sow-processing-results", "on_demand_throughput": [], "point_in_time_recovery": [{"enabled": false, "recovery_period_in_days": 0}], "range_key": "agent_type", "read_capacity": 0, "region": "us-east-1", "replica": [], "restore_date_time": null, "restore_source_name": null, "restore_source_table_arn": null, "restore_to_latest_time": null, "server_side_encryption": [], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-sow-processing-results", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-sow-processing-results", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeouts": null, "ttl": [{"attribute_name": "", "enabled": false}], "write_capacity": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "create_before_destroy": true}]}, {"module": "module.lambda", "mode": "data", "type": "archive_file", "name": "agent_operations_handler", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"exclude_symlink_directories": null, "excludes": null, "id": "3b52794d05d0f25556a7dd32bd1c9ab4bfa1c0b6", "output_base64sha256": "cF+2gxbOIdhJfLK8qhrBZqjCiUoKXSBAFNV3XzAnH1s=", "output_base64sha512": "ki7sK9HE/W3Dve7VNI8YD39JClQySwvyDTRW0NZZjnaYv3tfLOgKVNZ01lzzB7TzYlvH7h7B8zZC2Tw+8zGyVQ==", "output_file_mode": null, "output_md5": "7640cc3bc9cd1ba8d1b1e7b69ce69a6f", "output_path": "modules/lambda/agent_operations_handler.zip", "output_sha": "3b52794d05d0f25556a7dd32bd1c9ab4bfa1c0b6", "output_sha256": "705fb68316ce21d8497cb2bcaa1ac166a8c2894a0a5d204014d5775f30271f5b", "output_sha512": "922eec2bd1c4fd6dc3bdeed5348f180f7f490a54324b0bf20d3456d0d6598e7698bf7b5f2ce80a54d674d65cf307b4f3625bc7ee1ec1f33642d93c3ef331b255", "output_size": 3413, "source": [], "source_content": null, "source_content_filename": null, "source_dir": null, "source_file": "./src/agent_operations_handler.py", "type": "zip"}, "sensitive_attributes": []}]}, {"module": "module.lambda", "mode": "data", "type": "archive_file", "name": "general_operations_handler", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"exclude_symlink_directories": null, "excludes": null, "id": "c88a4bdde1a04d31a162797554562aed4d71050f", "output_base64sha256": "y2uXlLG6BZyF5soZOX291zellVTEE7GlE6f27kjprtU=", "output_base64sha512": "uk276m0rLZmHDdJZYiyHTnJRwbeoTrwPsg/Sb4TyhLhX4q3/gKHiFjR1XvltQVbfCzzGyV6NtQODk87AV5yZag==", "output_file_mode": null, "output_md5": "2ba589a644d014c8c0a1b6bb7b39c555", "output_path": "modules/lambda/general_operations_handler.zip", "output_sha": "c88a4bdde1a04d31a162797554562aed4d71050f", "output_sha256": "cb6b9794b1ba059c85e6ca19397dbdd737a59554c413b1a513a7f6ee48e9aed5", "output_sha512": "ba4dbbea6d2b2d99870dd259622c874e7251c1b7a84ebc0fb20fd26f84f284b857e2adff80a1e21634755ef96d4156df0b3cc6c95e8db5038393cec0579c996a", "output_size": 2748, "source": [], "source_content": null, "source_content_filename": null, "source_dir": null, "source_file": "./src/general_operations_handler.py", "type": "zip"}, "sensitive_attributes": []}]}, {"module": "module.lambda", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "**********85", "arn": "arn:aws:iam::**********85:user/nour", "id": "**********85", "user_id": "AIDAU6GDUSF4TSNFVXDKJ"}, "sensitive_attributes": []}]}, {"module": "module.lambda", "mode": "data", "type": "aws_iam_policy_document", "name": "lambda_assume_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"lambda.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["lambda.amazonaws.com"], "type": "Service"}], "resources": [], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.lambda", "mode": "data", "type": "aws_iam_policy_document", "name": "lambda_permissions", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"s3:PutObject\",\n        \"s3:ListBucket\",\n        \"s3:GetObject\",\n        \"s3:DeleteObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket/*\",\n        \"arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"dynamodb:UpdateItem\",\n        \"dynamodb:Scan\",\n        \"dynamodb:Query\",\n        \"dynamodb:PutItem\",\n        \"dynamodb:GetItem\",\n        \"dynamodb:DeleteItem\"\n      ],\n      \"Resource\": [\n        \"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results/index/*\",\n        \"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results\",\n        \"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status/index/*\",\n        \"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"bedrock:UseInferenceProfile\",\n        \"bedrock:InvokeModelWithResponseStream\",\n        \"bedrock:InvokeModel\",\n        \"bedrock:GetInferenceProfile\",\n        \"bedrock:GetFoundationModel\"\n      ],\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"bedrock-agent-runtime:ListAgents\",\n        \"bedrock-agent-runtime:ListAgentAliases\",\n        \"bedrock-agent-runtime:InvokeAgent\",\n        \"bedrock-agent-runtime:GetAgentAlias\",\n        \"bedrock-agent-runtime:GetAgent\"\n      ],\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"bedrock-agent:ListAgents\",\n        \"bedrock-agent:ListAgentAliases\",\n        \"bedrock-agent:GetAgentAlias\",\n        \"bedrock-agent:GetAgent\"\n      ],\n      \"Resource\": \"*\"\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"bedrock-agent-runtime:InvokeAgent\",\n      \"Resource\": [\n        \"arn:aws:bedrock-agent-runtime:us-east-1:**********85:agentAlias/*\",\n        \"arn:aws:bedrock-agent-runtime:us-east-1:**********85:agent/*\"\n      ]\n    },\n    {\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:GetCallerIdentity\",\n      \"Resource\": \"*\"\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Resource\":[\"arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket/*\",\"arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket\"]},{\"Effect\":\"Allow\",\"Action\":[\"dynamodb:UpdateItem\",\"dynamodb:Scan\",\"dynamodb:Query\",\"dynamodb:PutItem\",\"dynamodb:GetItem\",\"dynamodb:DeleteItem\"],\"Resource\":[\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results/index/*\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status/index/*\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status\"]},{\"Effect\":\"Allow\",\"Action\":[\"bedrock:UseInferenceProfile\",\"bedrock:InvokeModelWithResponseStream\",\"bedrock:InvokeModel\",\"bedrock:GetInferenceProfile\",\"bedrock:GetFoundationModel\"],\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"bedrock-agent-runtime:ListAgents\",\"bedrock-agent-runtime:ListAgentAliases\",\"bedrock-agent-runtime:InvokeAgent\",\"bedrock-agent-runtime:GetAgentAlias\",\"bedrock-agent-runtime:GetAgent\"],\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":[\"bedrock-agent:ListAgents\",\"bedrock-agent:ListAgentAliases\",\"bedrock-agent:GetAgentAlias\",\"bedrock-agent:GetAgent\"],\"Resource\":\"*\"},{\"Effect\":\"Allow\",\"Action\":\"bedrock-agent-runtime:InvokeAgent\",\"Resource\":[\"arn:aws:bedrock-agent-runtime:us-east-1:**********85:agentAlias/*\",\"arn:aws:bedrock-agent-runtime:us-east-1:**********85:agent/*\"]},{\"Effect\":\"Allow\",\"Action\":\"sts:GetCallerIdentity\",\"Resource\":\"*\"}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["s3:DeleteObject", "s3:GetObject", "s3:ListBucket", "s3:PutObject"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket", "arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket/*"], "sid": ""}, {"actions": ["dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status", "arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status/index/*", "arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results", "arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results/index/*"], "sid": ""}, {"actions": ["bedrock:GetFoundationModel", "bedrock:GetInferenceProfile", "bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream", "bedrock:UseInferenceProfile"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["bedrock-agent-runtime:GetAgent", "bedrock-agent-runtime:GetAgentAlias", "bedrock-agent-runtime:InvokeAgent", "bedrock-agent-runtime:ListAgentAliases", "bedrock-agent-runtime:ListAgents"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["bedrock-agent:GetAgent", "bedrock-agent:GetAgentAlias", "bedrock-agent:ListAgentAliases", "bedrock-agent:ListAgents"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}, {"actions": ["bedrock-agent-runtime:InvokeAgent"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:bedrock-agent-runtime:us-east-1:**********85:agent/*", "arn:aws:bedrock-agent-runtime:us-east-1:**********85:agentAlias/*"], "sid": ""}, {"actions": ["sts:GetCallerIdentity"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": ""}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"module": "module.lambda", "mode": "managed", "type": "aws_iam_role", "name": "lambda_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::**********85:role/dev-sow-lambda-role-20250818215842200300000001", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-08-18T21:58:42Z", "description": "", "force_detach_policies": false, "id": "dev-sow-lambda-role-20250818215842200300000001", "inline_policy": [{"name": "dev-sow-lambda-policy-20250818215907271400000007", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket/*\",\"arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket\"]},{\"Action\":[\"dynamodb:UpdateItem\",\"dynamodb:Scan\",\"dynamodb:Query\",\"dynamodb:PutItem\",\"dynamodb:GetItem\",\"dynamodb:DeleteItem\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results/index/*\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status/index/*\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status\"]},{\"Action\":[\"bedrock:UseInferenceProfile\",\"bedrock:InvokeModelWithResponseStream\",\"bedrock:InvokeModel\",\"bedrock:GetInferenceProfile\",\"bedrock:GetFoundationModel\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"bedrock-agent-runtime:ListAgents\",\"bedrock-agent-runtime:ListAgentAliases\",\"bedrock-agent-runtime:InvokeAgent\",\"bedrock-agent-runtime:GetAgentAlias\",\"bedrock-agent-runtime:GetAgent\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"bedrock-agent:ListAgents\",\"bedrock-agent:ListAgentAliases\",\"bedrock-agent:GetAgentAlias\",\"bedrock-agent:GetAgent\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":\"bedrock-agent-runtime:InvokeAgent\",\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:bedrock-agent-runtime:us-east-1:**********85:agentAlias/*\",\"arn:aws:bedrock-agent-runtime:us-east-1:**********85:agent/*\"]},{\"Action\":\"sts:GetCallerIdentity\",\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"], "max_session_duration": 3600, "name": "dev-sow-lambda-role-20250818215842200300000001", "name_prefix": "dev-sow-lambda-role-", "path": "/", "permissions_boundary": "", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "unique_id": "AROAU6GDUSF4V5T53E4UM"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda.data.aws_iam_policy_document.lambda_assume_role"], "create_before_destroy": true}]}, {"module": "module.lambda", "mode": "managed", "type": "aws_iam_role_policy", "name": "lambda_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-sow-lambda-role-20250818215842200300000001:dev-sow-lambda-policy-20250818215907271400000007", "name": "dev-sow-lambda-policy-20250818215907271400000007", "name_prefix": "dev-sow-lambda-policy-", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"s3:PutObject\",\"s3:ListBucket\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket/*\",\"arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket\"]},{\"Action\":[\"dynamodb:UpdateItem\",\"dynamodb:Scan\",\"dynamodb:Query\",\"dynamodb:PutItem\",\"dynamodb:GetItem\",\"dynamodb:DeleteItem\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results/index/*\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-processing-results\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status/index/*\",\"arn:aws:dynamodb:us-east-1:**********85:table/dev-sow-document-status\"]},{\"Action\":[\"bedrock:UseInferenceProfile\",\"bedrock:InvokeModelWithResponseStream\",\"bedrock:InvokeModel\",\"bedrock:GetInferenceProfile\",\"bedrock:GetFoundationModel\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"bedrock-agent-runtime:ListAgents\",\"bedrock-agent-runtime:ListAgentAliases\",\"bedrock-agent-runtime:InvokeAgent\",\"bedrock-agent-runtime:GetAgentAlias\",\"bedrock-agent-runtime:GetAgent\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"bedrock-agent:ListAgents\",\"bedrock-agent:ListAgentAliases\",\"bedrock-agent:GetAgentAlias\",\"bedrock-agent:GetAgent\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":\"bedrock-agent-runtime:InvokeAgent\",\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:bedrock-agent-runtime:us-east-1:**********85:agentAlias/*\",\"arn:aws:bedrock-agent-runtime:us-east-1:**********85:agent/*\"]},{\"Action\":\"sts:GetCallerIdentity\",\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "dev-sow-lambda-role-20250818215842200300000001"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.data.aws_caller_identity.current", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.lambda.data.aws_iam_policy_document.lambda_permissions", "module.s3.aws_s3_bucket.sow_main_bucket"]}]}, {"module": "module.lambda", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "lambda_basic_execution", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-sow-lambda-role-20250818215842200300000001/arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "role": "dev-sow-lambda-role-20250818215842200300000001"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda.aws_iam_role.lambda_role", "module.lambda.data.aws_iam_policy_document.lambda_assume_role"]}]}, {"module": "module.lambda", "mode": "managed", "type": "aws_lambda_function", "name": "agent_operations_handler", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-east-1:**********85:function:dev-sow-agent-operations", "code_sha256": "cF+2gxbOIdhJfLK8qhrBZqjCiUoKXSBAFNV3XzAnH1s=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"DOCUMENT_STATUS_TABLE": "dev-sow-document-status", "ENVIRONMENT": "dev", "MAIN_BUCKET_NAME": "**********85-us-east-1-dev-sow-main-bucket", "PROCESSING_RESULTS_TABLE": "dev-sow-processing-results"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": "modules/lambda/agent_operations_handler.zip", "function_name": "dev-sow-agent-operations", "handler": "agent_operations_handler.lambda_handler", "id": "dev-sow-agent-operations", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:**********85:function:dev-sow-agent-operations/invocations", "kms_key_arn": "", "last_modified": "2025-08-20T07:13:36.027+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/dev-sow-agent-operations", "system_log_level": ""}], "memory_size": 1024, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-east-1:**********85:function:dev-sow-agent-operations:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:**********85:function:dev-sow-agent-operations:$LATEST/invocations", "region": "us-east-1", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::**********85:role/dev-sow-lambda-role-20250818215842200300000001", "runtime": "python3.11", "s3_bucket": null, "s3_key": null, "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "cF+2gxbOIdhJfLK8qhrBZqjCiUoKXSBAFNV3XzAnH1s=", "source_code_size": 3413, "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeout": 900, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["data.aws_caller_identity.current", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.data.archive_file.agent_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"], "create_before_destroy": true}]}, {"module": "module.lambda", "mode": "managed", "type": "aws_lambda_function", "name": "general_operations_handler", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-east-1:**********85:function:dev-sow-general-operations", "code_sha256": "y2uXlLG6BZyF5soZOX291zellVTEE7GlE6f27kjprtU=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"DOCUMENT_STATUS_TABLE": "dev-sow-document-status", "ENVIRONMENT": "dev", "MAIN_BUCKET_NAME": "**********85-us-east-1-dev-sow-main-bucket", "PROCESSING_RESULTS_TABLE": "dev-sow-processing-results"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": "modules/lambda/general_operations_handler.zip", "function_name": "dev-sow-general-operations", "handler": "general_operations_handler.lambda_handler", "id": "dev-sow-general-operations", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:**********85:function:dev-sow-general-operations/invocations", "kms_key_arn": "", "last_modified": "2025-08-20T07:13:29.377+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/dev-sow-general-operations", "system_log_level": ""}], "memory_size": 512, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-east-1:**********85:function:dev-sow-general-operations:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:**********85:function:dev-sow-general-operations:$LATEST/invocations", "region": "us-east-1", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::**********85:role/dev-sow-lambda-role-20250818215842200300000001", "runtime": "python3.11", "s3_bucket": null, "s3_key": null, "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "y2uXlLG6BZyF5soZOX291zellVTEE7GlE6f27kjprtU=", "source_code_size": 2748, "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "timeout": 60, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["data.aws_caller_identity.current", "module.dynamodb.aws_dynamodb_table.document_status", "module.dynamodb.aws_dynamodb_table.processing_results", "module.lambda.aws_iam_role.lambda_role", "module.lambda.data.archive_file.general_operations_handler", "module.lambda.data.aws_iam_policy_document.lambda_assume_role", "module.s3.aws_s3_bucket.sow_main_bucket"], "create_before_destroy": true}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket", "name": "sow_documents_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::**********85-us-east-1-dev-sow-documents-bucket", "bucket": "**********85-us-east-1-dev-sow-documents-bucket", "bucket_domain_name": "**********85-us-east-1-dev-sow-documents-bucket.s3.amazonaws.com", "bucket_prefix": "", "bucket_region": "us-east-1", "bucket_regional_domain_name": "**********85-us-east-1-dev-sow-documents-bucket.s3.us-east-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "b513f9f41e8bd1f6a9c633360a39bddd5c8af818c1d67edbb0dd86b5e9f5745f", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "**********85-us-east-1-dev-sow-documents-bucket", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {"Project": "SOW Reviewer"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["data.aws_caller_identity.current"], "create_before_destroy": true}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket", "name": "sow_main_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket", "bucket": "**********85-us-east-1-dev-sow-main-bucket", "bucket_domain_name": "**********85-us-east-1-dev-sow-main-bucket.s3.amazonaws.com", "bucket_prefix": "", "bucket_region": "us-east-1", "bucket_regional_domain_name": "**********85-us-east-1-dev-sow-main-bucket.s3.us-east-1.amazonaws.com", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["POST", "GET", "PUT", "DELETE"], "allowed_origins": ["*"], "expose_headers": ["ETag"], "max_age_seconds": 3000}], "force_destroy": false, "grant": [{"id": "b513f9f41e8bd1f6a9c633360a39bddd5c8af818c1d67edbb0dd86b5e9f5745f", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "**********85-us-east-1-dev-sow-main-bucket", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {"Project": "SOW Reviewer"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["data.aws_caller_identity.current"], "create_before_destroy": true}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket", "name": "sow_reviews_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::**********85-us-east-1-dev-sow-reviews-bucket", "bucket": "**********85-us-east-1-dev-sow-reviews-bucket", "bucket_domain_name": "**********85-us-east-1-dev-sow-reviews-bucket.s3.amazonaws.com", "bucket_prefix": "", "bucket_region": "us-east-1", "bucket_regional_domain_name": "**********85-us-east-1-dev-sow-reviews-bucket.s3.us-east-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "b513f9f41e8bd1f6a9c633360a39bddd5c8af818c1d67edbb0dd86b5e9f5745f", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "**********85-us-east-1-dev-sow-reviews-bucket", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {"Project": "SOW Reviewer"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["data.aws_caller_identity.current"], "create_before_destroy": true}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_cors_configuration", "name": "sow_main_bucket_cors", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "**********85-us-east-1-dev-sow-main-bucket", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["DELETE", "GET", "POST", "PUT"], "allowed_origins": ["*"], "expose_headers": ["ETag"], "id": "", "max_age_seconds": 3000}], "expected_bucket_owner": "", "id": "**********85-us-east-1-dev-sow-main-bucket", "region": "us-east-1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.s3.aws_s3_bucket.sow_main_bucket"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "sow_main_bucket_encryption", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "**********85-us-east-1-dev-sow-main-bucket", "expected_bucket_owner": "", "id": "**********85-us-east-1-dev-sow-main-bucket", "region": "us-east-1", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.s3.aws_s3_bucket.sow_main_bucket"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_versioning", "name": "sow_main_bucket_versioning", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "**********85-us-east-1-dev-sow-main-bucket", "expected_bucket_owner": "", "id": "**********85-us-east-1-dev-sow-main-bucket", "mfa": null, "region": "us-east-1", "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.s3.aws_s3_bucket.sow_main_bucket"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_object", "name": "folder_structure", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "input/.keep", "schema_version": 0, "attributes": {"acl": null, "arn": "arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket/input/.keep", "bucket": "**********85-us-east-1-dev-sow-main-bucket", "bucket_key_enabled": false, "cache_control": "", "checksum_algorithm": null, "checksum_crc32": "", "checksum_crc32c": "", "checksum_crc64nvme": "", "checksum_sha1": "", "checksum_sha256": "", "content": "", "content_base64": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/octet-stream", "etag": "d41d8cd98f00b204e9800998ecf8427e", "force_destroy": false, "id": "**********85-us-east-1-dev-sow-main-bucket/input/.keep", "key": "input/.keep", "kms_key_id": null, "metadata": {}, "object_lock_legal_hold_status": "", "object_lock_mode": "", "object_lock_retain_until_date": "", "override_provider": [], "region": "us-east-1", "server_side_encryption": "AES256", "source": null, "source_hash": null, "storage_class": "STANDARD", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "version_id": "vbGMcl0OKXJ8L350cXsyPCplj7kKGjrQ", "website_redirect": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.s3.aws_s3_bucket.sow_main_bucket"]}, {"index_key": "output/.keep", "schema_version": 0, "attributes": {"acl": null, "arn": "arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket/output/.keep", "bucket": "**********85-us-east-1-dev-sow-main-bucket", "bucket_key_enabled": false, "cache_control": "", "checksum_algorithm": null, "checksum_crc32": "", "checksum_crc32c": "", "checksum_crc64nvme": "", "checksum_sha1": "", "checksum_sha256": "", "content": "", "content_base64": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/octet-stream", "etag": "d41d8cd98f00b204e9800998ecf8427e", "force_destroy": false, "id": "**********85-us-east-1-dev-sow-main-bucket/output/.keep", "key": "output/.keep", "kms_key_id": null, "metadata": {}, "object_lock_legal_hold_status": "", "object_lock_mode": "", "object_lock_retain_until_date": "", "override_provider": [], "region": "us-east-1", "server_side_encryption": "AES256", "source": null, "source_hash": null, "storage_class": "STANDARD", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "version_id": "pnJzvDVO_2dAUI29b2RU8EJk9fzAhy1f", "website_redirect": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.s3.aws_s3_bucket.sow_main_bucket"]}, {"index_key": "support_document/.keep", "schema_version": 0, "attributes": {"acl": null, "arn": "arn:aws:s3:::**********85-us-east-1-dev-sow-main-bucket/support_document/.keep", "bucket": "**********85-us-east-1-dev-sow-main-bucket", "bucket_key_enabled": false, "cache_control": "", "checksum_algorithm": null, "checksum_crc32": "", "checksum_crc32c": "", "checksum_crc64nvme": "", "checksum_sha1": "", "checksum_sha256": "", "content": "", "content_base64": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/octet-stream", "etag": "d41d8cd98f00b204e9800998ecf8427e", "force_destroy": false, "id": "**********85-us-east-1-dev-sow-main-bucket/support_document/.keep", "key": "support_document/.keep", "kms_key_id": null, "metadata": {}, "object_lock_legal_hold_status": "", "object_lock_mode": "", "object_lock_retain_until_date": "", "override_provider": [], "region": "us-east-1", "server_side_encryption": "AES256", "source": null, "source_hash": null, "storage_class": "STANDARD", "tags": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "tags_all": {"AutoShutdown": "true", "Backup": "false", "CostCenter": "Development", "CreatedBy": "Terraform", "Environment": "dev", "ManagedBy": "Terraform", "Owner": "Development Team", "Project": "SOW Reviewer", "Workspace": "default"}, "version_id": "vJadRIjYmUcC9zyiRnk5c1mPyIKGvZ_0", "website_redirect": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_caller_identity.current", "module.s3.aws_s3_bucket.sow_main_bucket"]}]}], "check_results": [{"object_kind": "var", "config_addr": "var.environment", "status": "pass", "objects": [{"object_addr": "var.environment", "status": "pass"}]}]}
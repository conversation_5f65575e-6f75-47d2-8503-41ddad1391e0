# Basic AWS WAFv2 WebACL for CloudFront
resource "aws_wafv2_web_acl" "frontend_waf" {
  provider = aws.useast1
  name        = "frontend-waf"
  description = "WAF for SOW reviewer Front End."
  scope       = "CLOUDFRONT"
  default_action {
    allow {}
  }
  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "frontendWAF"
    sampled_requests_enabled   = true
  }
  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 1
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "CommonRuleSet"
      sampled_requests_enabled   = true
    }
  }
}

# Example test user for Cognito User Pool (for demo/testing only)
resource "aws_cognito_user" "frontend_test_user" {
  user_pool_id = aws_cognito_user_pool.frontend_user_pool.id

  username     = var.test_user_username
  temporary_password = var.test_user_password
  attributes = {
    email = var.test_user_email
  }
  force_alias_creation = false
}

# Provider for us-east-1 (required for Lambda@Edge)
provider "aws" {
  alias  = "useast1"
  region = "us-east-1"
}

# IAM Role for Lambda@Edge function
resource "aws_iam_role" "lambda_edge_role" {
  provider = aws.useast1
  name_prefix = "${var.resource_prefix != null ? "${var.resource_prefix}-" : ""}lambda-edge-role-"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = ["lambda.amazonaws.com", "edgelambda.amazonaws.com"]
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(var.default_tags, {
    Environment = var.environment
    Project     = "SOW Reviewer"
    ManagedBy   = "Terraform"
  })
}

# IAM Policy for Lambda@Edge function
resource "aws_iam_role_policy" "lambda_edge_policy" {
  provider = aws.useast1
  name_prefix = "${var.resource_prefix != null ? "${var.resource_prefix}-" : ""}lambda-edge-policy-"
  role   = aws_iam_role.lambda_edge_role.id
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      }
    ]
  })
}

# Lambda@Edge function for Cognito authentication
resource "aws_lambda_function" "cognito_auth_edge" {
  provider      = aws.useast1
  filename      = data.archive_file.edge_function_code.output_path
  function_name = "${var.resource_prefix != null ? "${var.resource_prefix}-" : ""}cognito_auth_edge"
  role          = aws_iam_role.lambda_edge_role.arn
  handler       = "main.handler"
  runtime       = "nodejs18.x"
  publish       = true
  source_code_hash = data.archive_file.edge_function_code.output_base64sha256
  
  # Prevent destruction of Lambda@Edge functions as they can get stuck
  lifecycle {
    prevent_destroy = false
  }
}

# Cognito User Pool Domain for hosted UI
resource "aws_cognito_user_pool_domain" "frontend_user_pool_domain" {
  domain       = "${var.resource_prefix != null ? "${var.resource_prefix}-" : ""}${var.cognito_domain_prefix}"
  user_pool_id = aws_cognito_user_pool.frontend_user_pool.id
}

resource "aws_s3_bucket" "sow_frontend_bucket" {
	bucket = "${var.resource_prefix != null ? "${var.resource_prefix}-" : ""}${data.aws_caller_identity.current.account_id}-${var.region}-${var.project_name}-frontend-bucket"
}

# Allow public access for static website hosting
resource "aws_s3_bucket_public_access_block" "sow_frontend_bucket_public_access" {
  bucket                  = aws_s3_bucket.sow_frontend_bucket.id
  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}



resource "aws_s3_bucket_versioning" "sow_frontend_bucket_versioning_control" {
  bucket = aws_s3_bucket.sow_frontend_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# CORS configuration for static website hosting
resource "aws_s3_bucket_cors_configuration" "sow_frontend_bucket_cors" {
  bucket = aws_s3_bucket.sow_frontend_bucket.id
  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD", "PUT", "POST", "DELETE"]
    allowed_origins = ["*"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

# Enable static website hosting
resource "aws_s3_bucket_website_configuration" "sow_frontend_bucket_website" {
  bucket = aws_s3_bucket.sow_frontend_bucket.id

  index_document {
    suffix = "upload.html"
  }

  error_document {
    key = "upload.html"
  }
}

# Public read policy for static website hosting
resource "aws_s3_bucket_policy" "sow_frontend_bucket_public_read" {
  bucket = aws_s3_bucket.sow_frontend_bucket.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = "*"
        Action = ["s3:GetObject"]
        Resource = "${aws_s3_bucket.sow_frontend_bucket.arn}/*"
      }
    ]
  })
  depends_on = [aws_s3_bucket_public_access_block.sow_frontend_bucket_public_access]
}

//TODO: Update pages to be in a single uploaded file instead of uploading multiple pages.
resource "aws_s3_object" "website_object" {
  bucket = aws_s3_bucket.sow_frontend_bucket.id
  key    = "upload.html"
  source = "src/upload.html"
  content_type = "text/html"
}

resource "aws_s3_object" "website_object_2" {
  bucket = aws_s3_bucket.sow_frontend_bucket.id
  key    = "index.html"
  source = "src/index.html"
  content_type = "text/html"
}

# CloudFront distribution for static website hosting
resource "aws_cloudfront_distribution" "frontend_distribution" {
  origin {
    domain_name = aws_s3_bucket_website_configuration.sow_frontend_bucket_website.website_endpoint
    origin_id   = "s3-frontend-origin"
    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "http-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }

  aliases = [var.frontend_domain_name]
  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "upload.html"

  web_acl_id = aws_wafv2_web_acl.frontend_waf.arn

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "s3-frontend-origin"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400

    lambda_function_association {
      event_type   = "viewer-request"
      lambda_arn   = aws_lambda_function.cognito_auth_edge.qualified_arn
      include_body = false
    }
  }

  price_class = var.cloudfront_price_class

  restrictions {
    geo_restriction {
      restriction_type = "whitelist"
      locations        = var.geo_restriction_locations
    }
  }

  viewer_certificate {
    acm_certificate_arn = var.acm_certificate_arn
    ssl_support_method = "sni-only"
  }
}

# Cognito User Pool for authentication
resource "aws_cognito_user_pool" "frontend_user_pool" {
  name = "${var.resource_prefix != null ? "${var.resource_prefix}-" : ""}${var.project_name}-frontend-user-pool"

  auto_verified_attributes = ["email"]

  password_policy {
    minimum_length    = 8
    require_uppercase = true
    require_lowercase = true
    require_numbers   = true
    require_symbols   = false
  }
}

# Cognito User Pool Client
resource "aws_cognito_user_pool_client" "frontend_user_pool_client" {
  name         = "${var.resource_prefix != null ? "${var.resource_prefix}-" : ""}${var.project_name}-frontend-client"
  user_pool_id = aws_cognito_user_pool.frontend_user_pool.id
  generate_secret = false
  allowed_oauth_flows = ["code"]
  allowed_oauth_scopes = ["email", "openid", "profile"]
  allowed_oauth_flows_user_pool_client = true
  callback_urls = ["https://${var.frontend_domain_name}/"]
  logout_urls   = ["https://${var.frontend_domain_name}/"]
  supported_identity_providers = ["COGNITO"]
}
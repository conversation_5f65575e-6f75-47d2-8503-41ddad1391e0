from slack_sdk.models.dialogs import AbstractDialogSelector
from slack_sdk.models.dialogs import DialogChannelSelector
from slack_sdk.models.dialogs import DialogConversationSelector
from slack_sdk.models.dialogs import DialogExternalSelector
from slack_sdk.models.dialogs import DialogStaticSelector
from slack_sdk.models.dialogs import DialogTextArea
from slack_sdk.models.dialogs import DialogTextComponent
from slack_sdk.models.dialogs import DialogTextField
from slack_sdk.models.dialogs import DialogUserSelector
from slack_sdk.models.dialogs import TextElementSubtypes
from slack_sdk.models.dialogs import DialogBuilder

from slack import deprecation

deprecation.show_message(__name__, "slack_sdk.models.dialogs")

__all__ = [
    "AbstractDialogSelector",
    "DialogChannelSelector",
    "DialogConversationSelector",
    "DialogExternalSelector",
    "DialogStaticSelector",
    "Dialog<PERSON>ext<PERSON>rea",
    "DialogTextComponent",
    "DialogTextField",
    "DialogUserSelector",
    "TextElementSubtypes",
    "DialogBuilder",
]

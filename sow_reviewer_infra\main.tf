# # Get current AWS account and region information
# data "aws_caller_identity" "current" {}
# data "aws_region" "current" {}

# Local values for common tags and naming
locals {
  # Naming convention to avoid conflicts - more concise for IAM role limits
  name_prefix = var.resource_prefix != null ? "${var.resource_prefix}-" : ""
  
  # Common naming pattern - shorter for IAM compatibility
  resource_names = {
    project = var.project_name
    env     = var.environment
    prefix  = local.name_prefix
  }
  
  # Shorter naming for IAM roles to stay within limits
  iam_name_prefix = var.resource_prefix != null ? "${var.resource_prefix}-${var.environment}-" : "${var.environment}-"
  
  common_tags = merge(var.default_tags, {
    Environment   = var.environment
    Project       = "SOW Reviewer"
    ManagedBy     = "Terraform"
    Workspace     = terraform.workspace
  })
  
  # Environment-specific inference profile ARNs for Bedrock Agents
  foundation_model_arns = {
    dev     = "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"
    staging = "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"
    prod    = "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"
  }
  
  # Use provided foundation_model_arn or default based on environment
  foundation_model_arn = var.foundation_model_arn != null ? var.foundation_model_arn : local.foundation_model_arns[var.environment]
  
  # S3 Vectors configuration
  s3_vectors_bucket_name = var.s3_vectors_bucket_name != null ? var.s3_vectors_bucket_name : "${local.name_prefix}${data.aws_caller_identity.current.account_id}-${var.region}-${var.environment}-slack-vectors"
  s3_vectors_index_name = var.s3_vectors_index_name != null ? var.s3_vectors_index_name : "channel-summaries"
}

# S3 Module
module "s3" {
  source = "./modules/s3"
  
  account_id  = data.aws_caller_identity.current.account_id
  region      = var.region
  environment = var.environment
  project_name = local.resource_names.project
  name_prefix  = local.name_prefix
  tags        = local.common_tags
}

# DynamoDB Module
module "dynamodb" {
  source = "./modules/dynamodb"
  
  environment = var.environment
  project_name = local.resource_names.project
  name_prefix  = local.name_prefix
  tags        = local.common_tags
}

# Bedrock Agents Module
module "bedrock_agents" {
  source = "./modules/bedrock-agents"
  
  environment          = var.environment
  region               = var.region
  project_name         = local.resource_names.project
  name_prefix          = local.iam_name_prefix
  foundation_model_arn = local.foundation_model_arn
  tags                 = local.common_tags
}

# Lambda Module
module "lambda" {
  source = "./modules/lambda"
  
  environment                   = var.environment
  region                        = var.region
  project_name                  = local.resource_names.project
  name_prefix                   = local.iam_name_prefix
  main_bucket_name              = module.s3.main_bucket_name
  main_bucket_arn               = module.s3.main_bucket_arn
  slack_bucket_name             = module.s3.slack_bucket_name
  slack_bucket_arn              = module.s3.slack_bucket_arn
  document_status_table_name    = module.dynamodb.document_status_table_name
  document_status_table_arn     = module.dynamodb.document_status_table_arn
  processing_results_table_name = module.dynamodb.processing_results_table_name
  processing_results_table_arn  = module.dynamodb.processing_results_table_arn
  slack_metadata_table_name     = module.dynamodb.slack_metadata_table_name
  slack_metadata_table_arn      = module.dynamodb.slack_metadata_table_arn
  slack_channel_cache_table_name = module.dynamodb.slack_channel_cache_table_name
  slack_channel_cache_table_arn  = module.dynamodb.slack_channel_cache_table_arn
  slack_channel_reviews_table_name = module.dynamodb.slack_channel_reviews_table_name
  slack_channel_reviews_table_arn  = module.dynamodb.slack_channel_reviews_table_arn
  slack_bot_token               = var.slack_bot_token
  slack_app_token               = var.slack_app_token
  slack_workspace_id            = var.slack_workspace_id
  s3_vectors_bucket_name        = local.s3_vectors_bucket_name
  s3_vectors_index_name         = local.s3_vectors_index_name
  tags                          = local.common_tags
}

# API Gateway Module
module "api_gateway" {
  source = "./modules/api-gateway"
  
  environment                   = var.environment
  project_name                  = local.resource_names.project
  name_prefix                   = local.name_prefix
  upload_handler_function_name  = module.lambda.upload_handler_function_name
  upload_handler_invoke_arn     = module.lambda.upload_handler_invoke_arn
  agent_handler_function_name   = module.lambda.agent_handler_function_name
  agent_handler_invoke_arn      = module.lambda.agent_handler_invoke_arn
  status_handler_function_name  = module.lambda.status_handler_function_name
  status_handler_invoke_arn     = module.lambda.status_handler_invoke_arn
  list_handler_function_name    = module.lambda.list_handler_function_name
  list_handler_invoke_arn       = module.lambda.list_handler_invoke_arn
  tags                          = local.common_tags
}

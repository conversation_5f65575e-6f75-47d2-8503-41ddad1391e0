variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "project_name" {
  description = "Project name used in resource naming"
  type        = string
  default     = "sow-reviewer"
}

variable "name_prefix" {
  description = "Prefix for all resource names to avoid conflicts"
  type        = string
  default     = ""
}

variable "foundation_model_arn" {
  description = "ARN of the foundation model to use for Bedrock agents"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

data "aws_caller_identity" "current" {}

locals {
  is_foundation_model_arn       = can(regex("^arn:", var.foundation_model_arn))
  foundation_model_resource_arn = local.is_foundation_model_arn ? var.foundation_model_arn : "arn:aws:bedrock:${var.region}::foundation-model/${var.foundation_model_arn}"
}

# IAM permissions for Bedrock agents
data "aws_iam_policy_document" "bedrock_agent_trust_policy" {
  statement {
    sid     = "AmazonBedrockAgentInferenceProfilesCrossRegionPolicyProd"
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      identifiers = ["bedrock.amazonaws.com"]
      type        = "Service"
    }
    condition {
      test     = "StringEquals"
      values   = [data.aws_caller_identity.current.account_id]
      variable = "aws:SourceAccount"
    }
    condition {
      test     = "ArnLike"
      values   = ["arn:aws:bedrock:${var.region}:${data.aws_caller_identity.current.account_id}:agent/*"]
      variable = "aws:SourceArn"
    }
  }
}

data "aws_iam_policy_document" "bedrock_agent_permissions" {
  # Core Bedrock model permissions
  statement {
    effect = "Allow"
    actions = [
      "bedrock:InvokeModel",
      "bedrock:InvokeModelWithResponseStream",
      "bedrock:Converse",
      "bedrock:ConverseStream"
    ]
    resources = [
      "arn:aws:bedrock:*::foundation-model/*",
      "arn:aws:bedrock:*:${data.aws_caller_identity.current.account_id}:inference-profile/*"
    ]
  }
  
  # Specific inference profile permissions - required for Bedrock Agents
  statement {
    effect = "Allow"
    actions = [
      "bedrock:UseInferenceProfile",
      "bedrock:GetInferenceProfile",
      "bedrock:ListInferenceProfiles"
    ]
    resources = [
      "arn:aws:bedrock:*:${data.aws_caller_identity.current.account_id}:inference-profile/*"
    ]
  }

  # Foundation model access permissions
  statement {
    effect = "Allow"
    actions = [
      "bedrock:GetFoundationModel",
      "bedrock:ListFoundationModels"
    ]
    resources = ["*"]
  }

  # Bedrock Agent specific permissions
  statement {
    effect = "Allow"
    actions = [
      "bedrock-agent:*",
      "bedrock-agent-runtime:*"
    ]
    resources = ["*"]
  }
  
  # IAM permissions for Bedrock service role assumption
  statement {
    effect = "Allow"
    actions = [
      "iam:PassRole"
    ]
    resources = [
      "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*bedrock*"
    ]
    condition {
      test     = "StringEquals"
      variable = "iam:PassedToService"
      values   = ["bedrock.amazonaws.com"]
    }
  }
}


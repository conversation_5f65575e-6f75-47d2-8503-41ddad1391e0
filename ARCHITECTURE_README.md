# SOW Reviewer System - Architecture & Process Documentation

## Overview

The SOW (Statement of Work) Reviewer is a comprehensive cloud-native system built on AWS that automates the review process for SOW documents using AI-powered analysis. The system provides technical, financial, and legal reviews through specialized Bedrock agents, with additional Slack integration for project insights.

## System Architecture

### High-Level Architecture

The system follows a serverless, event-driven architecture with clear separation between frontend and backend components:

- **Frontend Layer**: Static web application with secure authentication
- **API Layer**: RESTful API Gateway with Lambda integrations
- **Processing Layer**: Specialized Lambda functions for different operations
- **AI Layer**: Amazon Bedrock agents for intelligent document analysis
- **Data Layer**: S3 for document storage, DynamoDB for metadata and results
- **Integration Layer**: Slack scraping and analysis for project insights

### Core Components

#### 1. Frontend Infrastructure
- **S3 Static Website**: Hosts HTML/CSS/JS files for the web interface
- **CloudFront CDN**: Global content delivery with SSL termination
- **Cognito User Pool**: Authentication and user management
- **Lambda@Edge**: Authentication enforcement at the edge
- **WAF**: Web Application Firewall for security protection

#### 2. API Gateway & Lambda Functions
- **API Gateway**: RESTful API with CORS support and four main endpoints
- **General Operations Lambda**: Handles uploads, status checks, document listing, and review fetching
- **Agent Operations Lambda**: Manages AI agent processing and document analysis

#### 3. AI Processing System
- **Technical Agent**: Reviews technical feasibility, SLAs, and resource requirements
- **Financial Agent**: Validates financial terms, AWS funding programs, and prerequisites
- **Legal Agent**: Identifies potential legal risks and problematic language

#### 4. Data Storage
- **Main S3 Bucket**: Organized customer-based folder structure
  - `{customer_name}/input/` - Original SOW documents
  - `{customer_name}/output/` - Generated review reports
  - `{customer_name}/support_document/` - Supporting materials
- **DynamoDB Tables**:
  - Document Status: Tracks processing states
  - Processing Results: Stores agent analysis outputs
  - Slack Metadata: Channel scraping cursors and timestamps
  - Slack Channel Reviews: AI analysis of project communications

#### 5. Slack Integration
- **Slack Scraper**: Automated message collection from channels
- **Slack Channel Reviewer**: AI analysis of project communications for SOW insights

## Process Flows

### 1. Document Upload & Processing Flow

```
User → Frontend → API Gateway → General Operations Lambda → S3 Storage → DynamoDB Status Update
```

### 2. AI Review Process Flow

```
User Request → API Gateway → Agent Operations Lambda → Bedrock Agent → Analysis Results → DynamoDB → S3 Output
```

### 3. Slack Analysis Flow

```
Slack API → Slack Scraper → S3 Storage → Slack Channel Reviewer → Bedrock Analysis → DynamoDB Results
```

## Component Roles & Responsibilities

### Frontend Components

#### Static Website (S3 + CloudFront)
- **Role**: User interface for document upload and review management
- **Responsibilities**:
  - Document upload interface
  - Review status tracking
  - Results visualization
  - Navigation between documents and reviews

#### Cognito Authentication
- **Role**: User authentication and authorization
- **Responsibilities**:
  - User registration and login
  - Session management
  - Token validation
  - Password policy enforcement

#### Lambda@Edge Authentication
- **Role**: Edge-level authentication enforcement
- **Responsibilities**:
  - Validate authentication tokens
  - Redirect unauthenticated users to login
  - Protect static content access

### Backend Components

#### General Operations Lambda
- **Role**: Core document management operations
- **Responsibilities**:
  - Handle document uploads to S3
  - Track document processing status
  - List available documents and reviews
  - Fetch completed review results
  - CORS handling for web requests

#### Agent Operations Lambda
- **Role**: AI-powered document analysis orchestration
- **Responsibilities**:
  - Route requests to appropriate Bedrock agents
  - Process technical, financial, and legal reviews
  - Integrate Slack channel insights for technical reviews
  - Store analysis results in DynamoDB and S3
  - Handle agent session management

### AI Processing Components

#### Technical Agent (Bedrock)
- **Role**: Technical feasibility and implementation review
- **Responsibilities**:
  - Identify overpromising on AI accuracy or SLAs
  - Flag missing technical specifications
  - Validate resource requirements for goals
  - Check for migration planning completeness
  - Integrate Slack channel insights for context

#### Financial Agent (Bedrock)
- **Role**: Financial terms and compliance validation
- **Responsibilities**:
  - Verify financial language accuracy
  - Validate AWS funding program requirements
  - Check MAP (Migration Acceleration Program) prerequisites
  - Ensure proper financial documentation

#### Legal Agent (Bedrock)
- **Role**: Legal risk assessment and language review
- **Responsibilities**:
  - Identify potentially problematic language
  - Flag scope creep risks
  - Check for unrealistic promises
  - Assess customer exploitation potential

### Data Management Components

#### S3 Storage System
- **Role**: Document and file storage with organized structure
- **Responsibilities**:
  - Store original SOW documents
  - Archive generated review reports
  - Maintain customer-based folder organization
  - Handle Slack message exports
  - Provide versioning and encryption

#### DynamoDB Tables
- **Role**: Metadata and results storage with fast access
- **Responsibilities**:
  - Track document processing status
  - Store agent analysis results
  - Maintain Slack scraping metadata
  - Provide query capabilities for reviews

### Integration Components

#### Slack Scraper Lambda
- **Role**: Automated Slack data collection
- **Responsibilities**:
  - Connect to Slack API with bot token
  - Scrape channel messages chronologically
  - Handle pagination and rate limiting
  - Store messages in organized S3 structure
  - Track scraping progress in DynamoDB

#### Slack Channel Reviewer Lambda
- **Role**: AI analysis of project communications
- **Responsibilities**:
  - Read and combine Slack channel files
  - Analyze content with Claude Sonnet 4
  - Extract project insights and recommendations
  - Store analysis results for future SOW reviews
  - Generate vector embeddings for similarity matching

## Security & Compliance

### Authentication & Authorization
- Cognito User Pool with hosted UI
- Lambda@Edge token validation
- HTTPS enforcement via CloudFront
- WAF protection against common attacks

### Data Protection
- S3 bucket encryption (AES-256)
- DynamoDB encryption at rest
- VPC endpoints for secure AWS service communication
- IAM roles with least privilege access

### Network Security
- CloudFront geo-restrictions
- API Gateway throttling and monitoring
- Private subnet deployment for sensitive components
- Security groups and NACLs for network isolation

## Monitoring & Logging

### CloudWatch Integration
- Lambda function logs and metrics
- API Gateway access and execution logs
- DynamoDB performance metrics
- S3 access logging

### Error Handling
- Comprehensive error logging in all Lambda functions
- Graceful degradation for service failures
- Retry mechanisms for transient failures
- Dead letter queues for failed processing

## Deployment & Environment Management

### Infrastructure as Code
- Terraform modules for all AWS resources
- Environment-specific configurations (dev/staging/prod)
- Automated deployment pipelines
- State management with S3 backend

### Environment Isolation
- Separate AWS accounts or regions per environment
- Environment-specific naming conventions
- Isolated data stores and processing pipelines
- Independent monitoring and alerting

This architecture provides a scalable, secure, and maintainable solution for automated SOW document review with AI-powered insights and comprehensive project context integration.

## API Endpoints

### General Operations API
- **POST /upload**: Upload SOW documents to S3 storage
- **GET /status**: Check document processing status
- **GET /documents**: List all uploaded documents
- **GET /reviews**: Fetch all completed reviews

### Agent Operations API
- **POST /process**: Trigger AI agent processing for specific review types

### Endpoint Details

#### Upload Endpoint
```
POST /upload
Content-Type: multipart/form-data
Body: {
  "file": <SOW document>,
  "customer_name": "Customer Name"
}
Response: {
  "document_id": "uuid",
  "status": "uploaded",
  "message": "Document uploaded successfully"
}
```

#### Process Endpoint
```
POST /process
Content-Type: application/json
Body: {
  "document_id": "uuid",
  "agent_type": "technical|financial|legal"
}
Response: {
  "status": "processing",
  "message": "Agent processing started"
}
```

#### Status Endpoint
```
GET /status?document_id=uuid
Response: {
  "document_id": "uuid",
  "status": "uploaded|processing|completed|error",
  "created_at": "timestamp",
  "customer_name": "Customer Name"
}
```

## Data Storage Schemas

### DynamoDB Table Structures

#### Document Status Table
- **Primary Key**: document_id (String)
- **Attributes**:
  - status: uploaded|processing|completed|error
  - created_at: ISO timestamp
  - customer_name: Customer identifier
  - file_name: Original document name
- **GSI**: StatusIndex (status, created_at)

#### Processing Results Table
- **Primary Key**: document_id (String)
- **Sort Key**: agent_type (String)
- **Attributes**:
  - review_content: Generated analysis text
  - processed_at: ISO timestamp
  - customer_name: Customer identifier
  - file_name: Original document name

#### Slack Metadata Table
- **Primary Key**: workspace_id (String)
- **Sort Key**: channel_id (String)
- **Attributes**:
  - last_scrape_ts: Unix timestamp
  - cursor: Slack API cursor
  - channel_name: Human-readable name
  - is_member: Boolean membership status

#### Slack Channel Reviews Table
- **Primary Key**: channel_id (String)
- **Sort Key**: review_date (String)
- **Attributes**:
  - channel_name: Human-readable name
  - bedrock_response: AI analysis results
  - bedrock_summary: Project summary
  - similar_channels: JSON array of similar projects
  - processing_time: Analysis duration in seconds

### S3 Storage Organization

#### Main Bucket Structure
```
{bucket-name}/
├── {customer_name}/
│   ├── input/
│   │   └── {document_id}.{extension}
│   ├── output/
│   │   ├── {document_id}_technical_review.txt
│   │   ├── {document_id}_financial_review.txt
│   │   └── {document_id}_legal_review.txt
│   └── support_document/
│       └── {additional_files}
```

#### Slack Bucket Structure
```
{slack-bucket-name}/
└── slack/
    └── {workspace_id}/
        └── channels/
            └── {channel_id}_{channel_name}/
                └── date={YYYY-MM-DD}/
                    ├── {timestamp1}.txt
                    ├── {timestamp2}.txt
                    └── ...
```

## Terraform Module Structure

### Root Configuration
- **main.tf**: Primary resource definitions and module calls
- **variables.tf**: Input variable definitions with validation
- **outputs.tf**: Output values for other systems
- **providers.tf**: AWS provider configuration
- **backend.tf**: Terraform state backend configuration

### Module Organization
```
modules/
├── api-gateway/          # REST API and routing
├── bedrock-agents/       # AI agents configuration
├── dynamodb/            # Database tables
├── lambda/              # Function definitions
└── s3/                  # Storage buckets
```

### Environment Management
```
environments/
├── dev.tfvars           # Development configuration
├── staging.tfvars       # Staging configuration
└── prod.tfvars          # Production configuration
```

## Deployment Process

### Prerequisites
1. AWS CLI configured with appropriate permissions
2. Terraform >= 1.6.0 installed
3. Access to Bedrock Claude Sonnet 4 model
4. SSL certificate for custom domain (ACM)
5. Slack bot token for integration (optional)

### Deployment Steps

#### 1. Initialize Terraform
```bash
cd sow_reviewer_infra
terraform init
```

#### 2. Plan Deployment
```bash
terraform plan -var-file="environments/dev.tfvars"
```

#### 3. Apply Configuration
```bash
terraform apply -var-file="environments/dev.tfvars"
```

#### 4. Package Lambda Functions
```bash
# Automated via Terraform data sources
# Functions are packaged from src/ directories
```

#### 5. Deploy Frontend Assets
```bash
# Static files deployed via Terraform
# S3 sync handled automatically
```

### Environment-Specific Configurations

#### Development Environment
- Reduced CloudFront price class
- Relaxed security policies
- Enhanced logging enabled
- Test user pre-created

#### Production Environment
- Global CloudFront distribution
- Strict security policies
- Audit logging enabled
- Multi-AZ deployment

## Monitoring & Observability

### CloudWatch Metrics
- Lambda function duration and errors
- API Gateway request counts and latency
- DynamoDB read/write capacity utilization
- S3 request metrics and storage usage

### Logging Strategy
- Structured JSON logging in all Lambda functions
- API Gateway access logs
- CloudFront access logs
- VPC Flow Logs for network analysis

### Alerting
- Lambda function error rates
- API Gateway 4xx/5xx error rates
- DynamoDB throttling events
- Bedrock API quota utilization

This comprehensive architecture enables efficient, scalable, and secure SOW document review with AI-powered analysis and rich project context integration.

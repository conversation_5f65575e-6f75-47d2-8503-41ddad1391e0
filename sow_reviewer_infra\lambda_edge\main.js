// Lambda@Edge function to require Cognito authentication for CloudFront
// This is a template file. The value for CLIENT_ID will be replaced by Terraform.

'use strict';

const cognitoDomain = "test-sowreviewer-frontend-domain.auth.us-east-1.amazoncognito.com";
const redirectUri = "https://sowreviewer.protagona.com/";
const client_id = "15cri4cqec6g8kgrsa7m4kc7c6";

exports.handler = (event, context, callback) => {

    const request = event.Records[0].cf.request;
    const headers = request.headers;

    // Helper to parse query string
    function getQueryParams(querystring) {
        const params = {};
        if (!querystring) return params;
        for (const part of querystring.split('&')) {
            const [key, value] = part.split('=');
            params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
        }
        return params;
    }

    // Parse query string for 'code' param
    const queryParams = getQueryParams(request.querystring);
    const code = queryParams['code'];

    // Step 2: If code is present, exchange it for tokens
    if (code) {
        // Use https module to make POST request to Cognito
        const https = require('https');
    const postData = 'grant_type=authorization_code&client_id=' + client_id + '&code=' + code + '&redirect_uri=' + encodeURIComponent(redirectUri);
        const options = {
            hostname: cognitoDomain,
            port: 443,
            path: '/oauth2/token',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => { data += chunk; });
            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const tokens = JSON.parse(data);
                        const idToken = tokens.id_token;
                        if (idToken) {
                            // Remove code param from URL for redirect
                            let cleanUrl = redirectUri;
                            if (request.uri && request.uri !== '/') {
                                cleanUrl = redirectUri.replace(/\/$/, '') + request.uri;
                            }
                            // If there were other query params, preserve them (except code)
                            const originalParams = getQueryParams(request.querystring);
                            delete originalParams['code'];
                            const paramStr = Object.keys(originalParams).map(function(k) { return encodeURIComponent(k) + '=' + encodeURIComponent(originalParams[k]); }).join('&');
                            if (paramStr) {
                                cleanUrl += '?' + paramStr;
                            }
                            const response = {
                                status: '302',
                                statusDescription: 'Found',
                                headers: {
                                    'set-cookie': [{ key: 'Set-Cookie', value: 'idToken=' + idToken + '; Path=/; Secure; HttpOnly' }],
                                    'location': [{ key: 'Location', value: cleanUrl }],
                                },
                            };
                            callback(null, response);
                        } else {
                            callback(null, { status: '401', statusDescription: 'Unauthorized', body: 'No id_token in response.' });
                        }
                    } catch (e) {
                        callback(null, { status: '500', statusDescription: 'Internal Server Error', body: 'Token parse error.' });
                    }
                } else {
                    callback(null, { status: '401', statusDescription: 'Unauthorized', body: 'Token exchange failed.' });
                }
            });
        });
        req.on('error', (e) => {
            callback(null, { status: '500', statusDescription: 'Internal Server Error', body: 'Token request error.' });
        });
        req.write(postData);
        req.end();
        return;
    }

    // Look for Cognito id token in cookies
    let idToken = null;
    if (headers.cookie) {
        for (const cookieHeader of headers.cookie) {
            const cookies = cookieHeader.value.split(';');
            for (const cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'idToken') {
                    idToken = value;
                }
            }
        }
    }

    if (!idToken) {
        // Not authenticated, redirect to Cognito Hosted UI
        if (!client_id) {
            // Fail gracefully if clientId is missing
            const response = {
                status: '500',
                statusDescription: 'Internal Server Error',
                body: 'Cognito client ID is not configured.'
            };
            callback(null, response);
            return;
        }
        const loginUrl = "https://" + cognitoDomain + "/login?client_id=" + client_id + "&response_type=code&scope=openid+profile+email&redirect_uri=" + encodeURIComponent(redirectUri);
        const response = {
            status: '302',
            statusDescription: 'Found',
            headers: {
                location: [{ key: 'Location', value: loginUrl }],
            },
        };
        callback(null, response);
        return;
    }

    // If token exists, allow request to proceed
    callback(null, request);
};
